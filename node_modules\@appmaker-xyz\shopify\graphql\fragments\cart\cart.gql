fragment cartResponse on Cart {
  appliedGiftCards {
    ...CartAppliedGiftCardFragment
  }
      delivery {
      addresses {
        address {
          ... on CartDeliveryAddress {
            address1
            address2
            zip
            provinceCode
            phone
            name
            longitude
            latitude
            lastName
            formattedArea
            firstName
            countryCode
            company
            city
          }
        }
        selected
        id
      }
    }
  deliveryGroups(first: 10) {
    nodes {
      selectedDeliveryOption {
        code
        deliveryMethodType
        description
        estimatedCost {
          amount
          currencyCode
        }
        handle
        title
      }
    }
  }
  id
  note
  createdAt
  checkoutUrl
  totalQuantity
  updatedAt
  estimatedCost {
    checkoutChargeAmount {
      amount
      currencyCode
    }
    subtotalAmount {
      amount
      currencyCode
    }
    totalAmount {
      amount
      currencyCode
    }
    totalDutyAmount {
      amount
      currencyCode
    }
    totalTaxAmount {
      amount
      currencyCode
    }
  }
  attributes {
    value
    key
  }
  lines(first: 250) {
    nodes {
      id
      attributes {
        key
        value
      }
      cost {
        amountPerQuantity {
          amount
          currencyCode
        }
        compareAtAmountPerQuantity {
          amount
          currencyCode
        }
        subtotalAmount {
          amount
          currencyCode
        }
        totalAmount {
          amount
          currencyCode
        }
      }
      discountAllocations {
        discountedAmount {
          amount
          currencyCode
        }
        ... on CartAutomaticDiscountAllocation {
          __typename
          discountApplication {
            targetType
            value {
              ... on MoneyV2 {
                __typename
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                __typename
                percentage
              }
            }
            allocationMethod
            targetSelection
          }
          discountedAmount {
            amount
            currencyCode
          }
          title
          targetType
        }
        ... on CartCodeDiscountAllocation {
          __typename
          code
          discountApplication {
            targetType
            value {
              ... on MoneyV2 {
                __typename
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                __typename
                percentage
              }
            }
            allocationMethod
            targetSelection
          }
          discountedAmount {
            amount
            currencyCode
          }
          targetType
        }
        ... on CartCustomDiscountAllocation {
          __typename
          discountedAmount {
            amount
            currencyCode
          }
          title
          discountApplication {
            targetType
            value {
              ... on MoneyV2 {
                __typename
                amount
                currencyCode
              }
              ... on PricingPercentageValue {
                __typename
                percentage
              }
            }
            allocationMethod
            targetSelection
          }
          targetType
        }
      }
      estimatedCost {
        amount {
          amount
          currencyCode
        }
        compareAtAmount {
          amount
          currencyCode
        }
        subtotalAmount {
          amount
          currencyCode
        }
        totalAmount {
          amount
          currencyCode
        }
      }
      id
      quantity
      merchandise {
        ... on ProductVariant {
          ...CartProductVariantFragment
          product {
            ...SimpleProductFragment
            collections(first: $collectionsFirst) {
              edges {
                node {
                  id
                  handle
                }
              }
            }
          }
        }
      }
    }
  }
  
  discountCodes {
    code
    applicable
  }
  discountAllocations {
    discountedAmount {
      amount
      currencyCode
    }
    ... on CartAutomaticDiscountAllocation {
      __typename
      discountApplication {
        targetType
        value {
          ... on MoneyV2 {
            __typename
            amount
            currencyCode
          }
          ... on PricingPercentageValue {
            __typename
            percentage
          }
        }
        allocationMethod
        targetSelection
      }
      discountedAmount {
        currencyCode
        amount
      }
      title
      targetType
    }
    ... on CartCodeDiscountAllocation {
      __typename
      code
      discountApplication {
        targetType
        value {
          ... on MoneyV2 {
            __typename
            amount
            currencyCode
          }
          ... on PricingPercentageValue {
            __typename
            percentage
          }
        }
        allocationMethod
        targetSelection
      }
      discountedAmount {
        amount
        currencyCode
      }
      targetType
    }
    ... on CartCustomDiscountAllocation {
      __typename
      discountedAmount {
        amount
        currencyCode
      }
      title
      discountApplication {
        targetType
        value {
          ... on MoneyV2 {
            __typename
            amount
            currencyCode
          }
          ... on PricingPercentageValue {
            __typename
            percentage
          }
        }
        allocationMethod
        targetSelection
      }
      targetType
    }
  }
  buyerIdentity {
    countryCode
    email
    phone
    # walletPreferences
  }
}

fragment CartProductVariantFragment on ProductVariant {
  availableForSale
  quantityAvailable
  sku
  id
  title
  selectedOptions {
    name
    value
  }
  image {
    thumbnail: url(transform: { maxWidth: 200 })
  }
  price {
    amount
    currencyCode
  }
  compareAtPrice {
    currencyCode
    amount
  }
}

fragment CartAppliedGiftCardFragment on AppliedGiftCard {
  amountUsedV2 {
    amount
    currencyCode
  }
  balanceV2 {
    amount
    currencyCode
  }
  id
  lastCharacters
  presentmentAmountUsed {
    amount
    currencyCode
  }
  balance {
    amount
    currencyCode
  }
  amountUsed {
    currencyCode
    amount
  }
}
