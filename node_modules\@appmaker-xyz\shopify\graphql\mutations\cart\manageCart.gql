mutation CartManage(
  $cartId: ID!
  $buyerIdentity: CartBuyerIdentityInput = {}
  $linesToAdd: [CartLineInput!] = []
  $discountCodes: [String!] = []
  $lineIdsToRemove: [ID!] = []
  $linesToUpdate: [CartLineUpdateInput!] = []
  $shouldUpdateBuyerIdentity: Boolean = false
  $shouldAddLines: Boolean = false
  $shouldUpdateDiscountCodes: Boolean = false
  $shouldRemoveLines: Boolean = false
  $shouldUpdateLines: Boolean = false
  $attributes: [AttributeInput!]!
  $shouldUpdateCartAttributes: Boolean = false
  $note: String = ""
  $shouldUpdateCartNote: Boolean = false
  $collectionsFirst: Int = 0
) {
  cartBuyerIdentityUpdate(cartId: $cartId, buyerIdentity: $buyerIdentity)
    @include(if: $shouldUpdateBuyerIdentity) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartLinesAdd(cartId: $cartId, lines: $linesToAdd)
    @include(if: $shouldAddLines) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartDiscountCodesUpdate(cartId: $cartId, discountCodes: $discountCodes)
    @include(if: $shouldUpdateDiscountCodes) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartLinesRemove(cartId: $cartId, lineIds: $lineIdsToRemove)
    @include(if: $shouldRemoveLines) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartLinesUpdate(cartId: $cartId, lines: $linesToUpdate)
    @include(if: $shouldUpdateLines) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartAttributesUpdate(cartId: $cartId, attributes: $attributes)
    @include(if: $shouldUpdateCartAttributes) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
  cartNoteUpdate(cartId: $cartId, note: $note)
    @include(if: $shouldUpdateCartNote) {
    cart {
      ...cartResponse
    }
    userErrors {
      ...cartError
    }
  }
}
