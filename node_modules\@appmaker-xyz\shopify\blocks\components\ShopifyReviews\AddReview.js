import React from 'react';
import { View, TextInput, StyleSheet } from 'react-native';
import { useAddReview } from '../../../hooks/useAddReview';
import { ThemeText, Button } from '@appmaker-xyz/ui';
import Icon from 'react-native-vector-icons/AntDesign';

function Input(props) {
  const { label, error, ...rest } = props;
  return (
    <View>
      <ThemeText category="bodySubText">{label}</ThemeText>
      {error && (
        <ThemeText category="bodySubText" style={styles.errorText}>
          {error}
        </ThemeText>
      )}
      <TextInput
        {...rest}
      />
    </View>
  );
}

function AddStarRating(props) {
  const { rating, setRating, label, error } = props;
  return (
    <View>
      <ThemeText category="bodySubText">{label}</ThemeText>
      {error && (
        <ThemeText category="bodySubText" style={styles.errorText}>
          {error}
        </ThemeText>
      )}
      <View style={styles.starRatingContainer}>
        {[1, 2, 3, 4, 5].map((item, index) => {
          return (
            <Icon
              name={index < rating ? 'star' : 'staro'}
              size={32}
              color={index < rating ? '#FBBF24' : '#FBBF24'}
              onPress={() => setRating(index + 1)}
              style={styles.starIcon}
              key={index}
            />
          );
        })}
      </View>
    </View>
  );
}

const AddReview = (props) => {
  const {
    onSubmitReview,
    isLoading,
    setName,
    setRating,
    setEmail,
    setBody,
    setTitle,
    name,
    email,
    rating,
    body,
    title,
    user,
    validationErrors,
  } = useAddReview(props);

  return (
    <View>
      <Input
        label="Name"
        placeholder="Enter your name (public)"
        onChangeText={setName}
        value={name}
        error={validationErrors?.name}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Email"
        placeholder="Enter your email (private)"
        onChangeText={setEmail}
        value={email}
        error={validationErrors?.email}
        editable={user?.email ? false : true}
        style={[styles.input, styles.inputHeight]}
      />
      <AddStarRating
        label={`Rating (${rating})`}
        rating={rating}
        setRating={setRating}
        error={validationErrors?.rating}
      />
      <Input
        label="Review Title"
        placeholder="Give your review a title"
        onChangeText={setTitle}
        value={title}
        error={validationErrors?.title}
        style={[styles.input, styles.inputHeight]}
      />
      <Input
        label="Review"
        placeholder="Write your review here"
        onChangeText={setBody}
        value={body}
        error={validationErrors?.body}
        multiline
        numberOfLines={4}
        style={[styles.input, styles.textAreaHeight]}
      />
      {validationErrors?.general && (
        <ThemeText category="bodySubText" style={styles.errorText}>
          {validationErrors.general}
        </ThemeText>
      )}
      <Button
        onPress={onSubmitReview}
        isLoading={isLoading}
        color="#212121"
        title="Submit Review"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    borderRadius: 5,
    marginBottom: 10,
    marginTop: 2,
  },
  inputHeight: {
    height: 46,
  },
  textAreaHeight: {
    height: 100,
    textAlignVertical: 'top',
  },
  starRatingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 2,
  },
  starIcon: {
    marginRight: 5,
    padding: 5,
  },
  errorText: {
    color: '#FF0000',
    fontSize: 12,
    marginTop: 4,
  },
});

export default AddReview;
