import React from 'react';
import { But<PERSON>, StepperButton } from '@appmaker-xyz/ui';
import { useProductCartQuantity } from '@appmaker-xyz/shopify';
import { StyleSheet } from 'react-native';
import { appmaker } from '@appmaker-xyz/core';

const ProductGridCTA = (props) => {
  const {
    addCartButtonType,
    productType,
    inStock,
    variant,
    isAddToCartLoading,
    buttonStyle,
    toggleWishList,
    isSaved,
    onQuantityChange,
    updateCart,
    min = 1,
    max = 10000,
    step = 1,
    count = 0,
    openProduct,
    addToCart,
    customStyles,
    showVariations,
    preOrderEnabled,
  } = props;
  const [cartQuantity, cartLineItem] = useProductCartQuantity({
    variantId: variant?.node?.id,
  });

  const onAddToCart = () => {
    onQuantityChange(1, variant);
  };
  const incrementQuantity = () => {
    let countNow = cartQuantity;
    if (countNow < parseFloat(max)) {
      countNow = parseFloat(countNow) + parseFloat(step);
    }
    // setAdding(true);
    const lineItemId = cartLineItem?.id;

    updateCart({
      quantity: countNow,
      variant: variant,
      cartLineItemId: lineItemId,
    });
  };

  const decrementQuantity = () => {
    let countNow = cartQuantity;
    const lineItemId = cartLineItem?.id;
    if (countNow > min) {
      countNow = parseFloat(countNow) - parseFloat(step);
      updateCart({
        quantity: countNow,
        variant: variant,
        cartLineItemId: lineItemId,
      });
    } else if (countNow === min) {
      updateCart({
        quantity: 0,
        variant: variant,
        cartLineItemId: lineItemId,
      });
    }
  };

  const productGridCtaButtonColor = appmaker.applyFilters(
    'product-grid-cta-button-color',
  );
  const productGridCtaButtonTextColor = appmaker.applyFilters(
    'product-grid-cta-button-text-color',
  );

  const buttonStyles = [styles.button];
  const WishlistButton = () => (
    <Button
      size="sm"
      isOutline={true}
      color="#212121"
      activityIndicatorColor="#212121"
      title={!isSaved ? 'ADD TO WISHLIST' : 'ADDED TO WISHLIST'}
      buttonStyle={buttonStyle}
      fontType="medium"
      onPress={() => {
        toggleWishList && toggleWishList();
      }}
    />
  );

  const ctaText = productType === 'variable' ? 'SELECT OPTION' : 'ADD TO CART';
  const finalCtaText = showVariations ? 'ADD TO CART' : ctaText;

  const ctaAction = productType === 'variable' ? openProduct : addToCart;
  const finalCtaAction = showVariations ? onAddToCart : ctaAction;

  return addCartButtonType
    ? {
        none: null,
        'grocery-mode': inStock ? (
          <StepperButton
            onAddToCart={onAddToCart}
            quantity={cartQuantity}
            fullWidth={true}
            isLoading={isAddToCartLoading}
            increaseQuantity={incrementQuantity}
            decreaseQuantity={decrementQuantity}
            groceryMode={true}
            textColor={customStyles?.initial_button?.text?.color}
            containerStyle={[
              buttonStyles,
              buttonStyle,
              customStyles?.initial_button?.container,
            ]}
          />
        ) : (
          <WishlistButton />
        ),
        'add-cart-button': inStock ? (
          <Button
            size="sm"
            title={finalCtaText}
            activityIndicatorColor={productGridCtaButtonTextColor}
            isLoading={isAddToCartLoading}
            onPress={finalCtaAction}
            buttonStyle={[buttonStyles, buttonStyle]}
            color={productGridCtaButtonColor}
            textColor={productGridCtaButtonTextColor}
            fontType="bold"
          />
        ) : preOrderEnabled ? (
            <Button
            size="sm"
            title="Pre Order"
            isOutline={true}
            color="#212121"
            activityIndicatorColor="#212121"
            buttonStyle={buttonStyle}
            fontType="medium"
            onPress={onAddToCart}
          />
        ) : (
          <WishlistButton />
        ),
      }[addCartButtonType]
    : null;
};

const styles = StyleSheet.create({
  button: { height: 32, justifyContent: 'center' },
});

export default ProductGridCTA;
