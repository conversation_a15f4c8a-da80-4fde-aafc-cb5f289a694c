import React from 'react';
import { StyleSheet, View, ViewStyle, TextStyle } from 'react-native';
import MultiSlider from '@ptomasroos/react-native-multi-slider';
import { useSelectedFilterItem } from '@appmaker-xyz/shopify';
import { ThemeText } from '../atoms';

interface FilterItem {
  values: Array<{
    input: string;
    availableMin?: number;
    availableMax?: number;
  }>;
}

interface MinMax {
  min: number;
  max: number;
  availableMin?: number;
  availableMax?: number;
}

function getMinMax(item: FilterItem): MinMax {
  let min = 0;
  let max = 10000;
  try {
    const config = JSON.parse(item?.values[0].input);
    min = config.price.min;
    max = config.price.max;
  } catch (error) {}
  return {
    min,
    max,
    availableMin: item?.values?.[0]?.availableMin,
    availableMax: item?.values?.[0]?.availableMax,
  };
}

interface CustomLabelProps {
  oneMarkerValue: string;
  twoMarkerValue: string;
  labelContainerStyle?: ViewStyle;
  labelTextStyle?: TextStyle;
}

function CustomLabel({
  oneMarkerValue,
  twoMarkerValue,
  labelContainerStyle,
  labelTextStyle,
}: CustomLabelProps) {
  return (
    <View style={[styles.customLabelContainer, labelContainerStyle]}>
      <ThemeText style={[styles.customLabelText, labelTextStyle]}>
        {oneMarkerValue}
      </ThemeText>
      <ThemeText style={[styles.customLabelText, labelTextStyle]}>
        {twoMarkerValue}
      </ThemeText>
    </View>
  );
}

interface FilterPriceRangeProps {
  item: FilterItem;
  filterKey: string;
  selectFilter: (filterKey: string, filterValueId: string, value: any) => void;
  sliderLength?: number;
  containerStyle?: ViewStyle;
  trackStyle?: ViewStyle;
  selectedStyle?: ViewStyle;
  markerStyle?: ViewStyle;
  pressedMarkerStyle?: ViewStyle;
  labelContainerStyle?: ViewStyle;
  labelTextStyle?: TextStyle;
}

function FilterPriceRange({
  item,
  filterKey,
  selectFilter,
  sliderLength = 180,
  containerStyle,
  trackStyle,
  selectedStyle,
  markerStyle,
  pressedMarkerStyle,
  labelContainerStyle,
  labelTextStyle,
}: FilterPriceRangeProps) {
  const selectedItem = useSelectedFilterItem({
    filterKey,
    filterValueId: filterKey,
  });
  const { min, max, availableMin, availableMax } = getMinMax(item);

  if (min === max || min > max) {
    return null;
  }

  return (
    <View style={[styles.sliderContainer, containerStyle]}>
      <MultiSlider
        values={[selectedItem?.min || min, selectedItem?.max || max]}
        sliderLength={sliderLength}
        enableLabel={true}
        customLabel={(props) => (
          <CustomLabel
            {...props}
            labelContainerStyle={labelContainerStyle}
            labelTextStyle={labelTextStyle}
          />
        )}
        isMarkersSeparated={true}
        trackStyle={[styles.trackStyle, trackStyle]}
        selectedStyle={[styles.slideSelectedStyle, selectedStyle]}
        markerStyle={[styles.markerStyle, markerStyle]}
        pressedMarkerStyle={[styles.markerPressed, pressedMarkerStyle]}
        min={availableMin || min}
        max={availableMax || max}
        onValuesChangeFinish={(values: number[]) => {
          const [minValue, maxValue] = values;
          selectFilter(filterKey, filterKey, {
            type: 'PRICE_RANGE',
            min: minValue,
            max: maxValue,
          });
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  trackStyle: {
    borderRadius: 8,
    height: 2,
  },
  slideSelectedStyle: {
    backgroundColor: '#212121',
  },
  markerStyle: {
    height: 20,
    width: 20,
    borderRadius: 16,
    backgroundColor: '#212121',
    borderWidth: 0.5,
    borderColor: 'transparent',
  },
  markerPressed: {
    backgroundColor: '#4F4F4F',
  },
  sliderContainer: {
    alignItems: 'center',
    paddingVertical: 2,
  },
  customLabelContainer: {
    position: 'relative',
    justifyContent: 'space-between',
    flexDirection: 'row',
    width: 190,
    left: -4,
    top: 6,
  },
  customLabelText: {
    color: 'black',
    fontSize: 12,
  },
});

export default FilterPriceRange;
