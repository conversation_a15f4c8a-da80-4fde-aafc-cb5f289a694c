import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { ThemeText, AppTouchable } from '../atoms';
import EmptyBagIcon from './assets/EmptyState.svg';
import NotFound from './assets/NotFound.svg';
import { useNavigationState } from '@react-navigation/native';

const dimensions = {
  fullHeight: Dimensions.get('window').height,
  fullWidth: Dimensions.get('window').width,
};

const EmptyBlock = ({ attributes = {}, onAction }) => {
  const {
    title,
    description,
    buttonText = 'Continue Shopping',
    type,
  } = attributes;
  const navigationState = useNavigationState((state) => state);
  const isInTabNavigator = navigationState?.type === 'tab';
  function icon() {
    switch (type) {
      case 'empty-cart':
        return <EmptyBagIcon height={150} width={200} style={styles.svgIcon} />;
      case 'not-found':
        return <NotFound height={150} width={200} style={styles.svgIcon} />;
      default:
        return null;
    }
  }
  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        {icon()}
        {title ? (
          <ThemeText size="lg" fontFamily="medium" style={styles.title}>
            {title}
          </ThemeText>
        ) : null}
        {description ? (
          <ThemeText size="sm" color="#6B7280" style={styles.description}>
            {description}
          </ThemeText>
        ) : null}
        {onAction && !isInTabNavigator && type === 'empty-cart' ? (
          <AppTouchable style={styles.button} onPress={()=>onAction({
            action: 'GO_BACK',
          })}>
            <ThemeText color="#ffffff" fontFamily="medium">
              {buttonText}
            </ThemeText>
          </AppTouchable>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: dimensions.fullHeight,
    backgroundColor: '#fff',
  },
  innerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
    paddingHorizontal: 16,
  },
  title: {
    marginBottom: 6,
    textAlign: 'center',
  },
  description: {
    textAlign: 'center',
  },
  svgIcon: {
    marginTop: -80,
  },
  button: {
    paddingHorizontal: 18,
    paddingVertical: 10,
    backgroundColor: '#212121',
    marginTop: 18,
    borderRadius: 6,
  },
});

export default EmptyBlock;