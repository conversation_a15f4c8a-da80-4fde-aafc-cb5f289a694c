import { DataSourceConfig, useDataSourceV2 } from '@appmaker-xyz/core';
import React, { useEffect } from 'react';
import { Text, View } from 'react-native';
import { AxiosResponse } from 'axios';
import {
  BlockScreen,
  SwipeNavigationPageConfig,
  SwipeNavigationScreenRouteProp,
  SwipeNavigationStoreState,
  SwipeNavigatorActionParams,
  useSwipeNavigationStore,
} from '@appmaker-xyz/react-native';
import {
  ShopifyDataSourceConfig,
  Product,
  singleProductParams,
  singleProductContext,
} from '@appmaker-xyz/shopify';
import { isEmpty } from 'lodash';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { analytics } from '@appmaker-xyz/core';
import { useNavigation } from '@react-navigation/native';

type SingleProductQuery = {
  productHandle?: string;
  productId?: string;
};
function getActionParams(
  route: SwipeNavigationScreenRouteProp,
): SwipeNavigatorActionParams<SingleProductQuery> {
  return route?.params?.action?.params;
}
function getCollectionHandle(route: SwipeNavigationScreenRouteProp) {
  try {
    return getActionParams(route).collection;
  } catch (error) {
    return {};
  }
}
function getInitialProduct(route: SwipeNavigationScreenRouteProp) {
  try {
    return getActionParams(route).initialItem;
  } catch (error) {
    return {};
  }
}
function getInitialProductIndex(route: SwipeNavigationScreenRouteProp): number {
  try {
    return getActionParams(route).initialItemIndex || 0;
  } catch (error) {
    return 0;
  }
}

function getInitialProductId(
  route: SwipeNavigationScreenRouteProp,
): string | undefined {
  try {
    return getActionParams(route).initialItemQuery.productId;
  } catch (error) {
    return undefined;
  }
}

function getInitialProductHandle(
  route: SwipeNavigationScreenRouteProp,
): string | undefined {
  try {
    return getActionParams(route).initialItemQuery.productHandle;
  } catch (error) {
    return undefined;
  }
}
type ItemInput<T> = {
  item: T;
  index: number;
};

type InitialItemInput = {
  route: SwipeNavigationScreenRouteProp;
};
function ProductScreen({ productNode, route }) {
  const params = route?.params?.action?.params || {};
  const navigation = useNavigation();
  return (
    <BlockScreen
      route={{
        params: {
          blockListKey: `flatlist-${productNode?.id}`,
          navigation,
          action: {
            params: {
              ...params,
              pageData: {
                node: productNode,
              },
            },
          },
          pageId: 'productDetail',
        },
      }}
    />
  );
}
const MemoizedProductScreen = React.memo(ProductScreen);
function InitialItem({ route, currentItem }) {
  const initialProduct = getInitialProduct(route);
  const hasInitialProduct = !!initialProduct;
  const setState = useSwipeNavigationStore<SwipeNavigationStoreState>(
    (state) => state.setState,
  );

  const [{ data, isLoading }, { item }] = useDataSourceV2<
    Product,
    AxiosResponse
  >({
    dataSource: ShopifyDataSourceConfig.Product({
      productHandle: getInitialProductHandle(route),
      productId: getInitialProductId(route),
    }),
    enabled: !hasInitialProduct && !currentItem,
    filterParams: {},
  });
  useEffect(() => {
    if (!hasInitialProduct && item) {
      setState(item);
    }
  }, [item]);
  if(currentItem) {
    return <MemoizedProductScreen productNode={currentItem.node || currentItem} route={route} />;
  }

  if (hasInitialProduct) {
    return <MemoizedProductScreen productNode={initialProduct.node || initialProduct} route={route} />;
  }
  return isLoading ? (
    <View style={{ padding: 12 }}>
      <SkeletonPlaceholder borderRadius={4} marginTop={12}>
        <SkeletonPlaceholder.Item
          width={'auto'}
          height={450}
          marginBottom={12}
        />
        <SkeletonPlaceholder.Item
          width={'auto'}
          height={75}
          marginBottom={12}
        />
        <SkeletonPlaceholder.Item
          width={'auto'}
          height={50}
          marginBottom={12}
        />
      </SkeletonPlaceholder>
    </View>
  ) : (
    <MemoizedProductScreen productNode={item} route={route} />
  );
}
const navigator: SwipeNavigationPageConfig<Product> = {
  getInitialItem({ route }) {
    const initialProduct = getInitialProduct(route);
    return initialProduct;
  },
  getInitialItemIndex({ route }) {
    return getInitialProductIndex(route);
  },
  renderInitialItem({ route, currentItem }) {
    return <InitialItem route={route} currentItem={currentItem} />;
  },
  renderItem({ item, index }: ItemInput<Product>) {
    return <MemoizedProductScreen productNode={item.node || item} />;
  },
  getDataSourceConfig: function getDataSourceConfig(
    route: SwipeNavigationScreenRouteProp,
    state: SwipeNavigationStoreState,
  ) {
    const actionParams = getActionParams(route);
    let currentDataSource = actionParams.listDataSource;
    if (isEmpty(currentDataSource) && isEmpty(state)) return;
    return currentDataSource;
  },
  getFilterParams(route: SwipeNavigationScreenRouteProp) {
    return route?.params?.action?.params?.filterParams || {};
  },
  onItemChange({ currentItem: productData = {}, direction, index }) {
    if (productData) {
      analytics.track(
        'product_swiped',
        singleProductParams(
          productData,
          productData?.node?.variants?.edges?.[0] || productData?.variants?.edges?.[0],
        ),
        singleProductContext(productData, null, {
          pageId: '',
          direction,
          index,
        }),
      );
    }
  },
};
export { navigator as ProductNavigatorPage };
