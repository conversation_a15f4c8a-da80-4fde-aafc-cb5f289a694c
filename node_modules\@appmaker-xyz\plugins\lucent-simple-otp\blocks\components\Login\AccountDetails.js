import React, { useState } from 'react';
import { View, StyleSheet, Pressable } from 'react-native';
import { useAccountDetailUpdate } from '@appmaker-xyz/shopify';
import { ThemeText } from '@appmaker-xyz/ui';
import InputBox from './components/InputBox';
import { ActivityIndicator } from 'react-native';

const AccountDetails = (props) => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    repeat_password: '',
    first_name: '',
    last_name: '',
    phone: '',
    accepts_marketing: true,
    agree_terms: false,
  });
  const [secure, setSecure] = useState(true);
  const [loading, setLoading] = useState(false);
  // const { register } = useUser({ onAction });
  const {
    email,
    firstName,
    lastName,
    phone,
    isEmailDisabled,
    isPhoneDisabled,
    updateAccountDetails,
  } = useAccountDetailUpdate(props);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      await updateAccountDetails({
        firstName: textFirstName,
        lastName: textLastName,
        phone: textMobile,
        email: textEmail,
      });
      setLoading(false);
    } catch (e) {
      setLoading(false);
    }
  };

  const [textFirstName, setFirstName] = useState(firstName);
  const [textLastName, setLastName] = useState(lastName);
  const [textMobile, setMobileNumber] = useState(phone);
  const [textEmail, setEmail] = useState(email);
  return (
    <View style={styles.container}>
      <View style={styles.viewRow}>
        <InputBox
          mainContainerStyle={styles.flex1}
          backgroundStyle={[styles.inputContainerStyle, styles.marginR5]}
          placeholder={'First name*'}
          titleText={'First Name'}
          isErrorMessageEnabled={true}
          // showError={isShowError}
          // errorMessage={isShowError ? errorFirstName : ''}
          value={textFirstName}
          onChangeText={(text) => setFirstName(text)}
        />
        <InputBox
          mainContainerStyle={styles.flex1}
          backgroundStyle={[styles.inputContainerStyle, styles.marginL5]}
          titleTextStyle={styles.marginL5}
          placeholder={'Last name'}
          titleText={'Last Name'}
          isErrorMessageEnabled={true}
          // showError={isShowError}
          // errorMessage={''}
          value={textLastName}
          onChangeText={(text) => setLastName(text)}
        />
      </View>
      <InputBox
        backgroundStyle={styles.inputContainerStyle}
        placeholder={'Mobile Number'}
        keyboardType="phone-pad"
        titleText={'Phone Number'}
        disabled={isPhoneDisabled}
        isErrorMessageEnabled={true}
        // showError={isShowError}
        // errorMessage={''}
        value={textMobile}
        onChangeText={(text) => setMobileNumber(text)}
      />
      <InputBox
        backgroundStyle={styles.inputContainerStyle}
        placeholder={'Email Id*'}
        keyboardType="email-address"
        titleText={'Email Id'}
        disabled={isEmailDisabled}
        isErrorMessageEnabled={true}
        value={textEmail || ''}
        // showError={isShowError}
        // errorMessage={isShowError ? errorEmailId : ''}
        // value={isPhoneDisabled && !isEmailDisabled ? '' : textEmail}
        onChangeText={(text) => setEmail(text)}
      />

      <Pressable
        style={styles.buttonBgStyle}
        disabled={loading}
        onPress={handleSubmit}>
        {loading ? (
          <ActivityIndicator size={18} color="#fff" />
        ) : (
          <ThemeText color="#fff">SIGN UP</ThemeText>
        )}
      </Pressable>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 60,
    backgroundColor: '#fff',
    paddingHorizontal: 25,
    paddingTop: 40,
  },
  viewRow: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  buttonBgStyle: {
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
    height: 46,
    borderRadius: 6,
    marginTop: 40,
  },
  buttonTextStyle: {
    fontSize: 14,
    color: 'white',
  },
  inputContainerStyle: {
    marginVertical: 5,
  },
  marginR5: {
    marginRight: 5,
  },
  marginL5: {
    marginLeft: 5,
  },
  viewRadioMain: {
    flexDirection: 'row',
    marginTop: 25,
  },
  marginH35: {
    marginHorizontal: 35,
  },
});

export default AccountDetails;
