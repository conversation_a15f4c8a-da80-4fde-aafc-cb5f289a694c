import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { Layout, ThemeText } from '../atoms';
import { StepperButton } from '../coreComponents';
import { usePageState } from '@appmaker-xyz/core';
import { useProductDetailV2 } from '@appmaker-xyz/shopify';

const CounterBlock = ({ attributes, ...props }) => {
  const {
    title = 'Select Count',
    id = 'quantity',
    min_value = 1,
    max_value,
    label,
    input_value,
    step,
    type,
    fullWidth,
    themeColor,
    __appmakerCustomStyles = {},
  } = attributes;
  const [count, setCount] = useState(min_value);
  const setPageState = usePageState((state) => state.setPageState);
  const quantity = usePageState((state) => state.quantity);
  const { checkStockAvailability } = useProductDetailV2(props);

  const handleQuantity = (num) => {
    setCount((prevCount) => {
      const newCount = prevCount + num;
      // Ensure count doesn't go below min_value or above max_value
      if (newCount < min_value) {
        return min_value;
      } else if (max_value !== undefined && newCount > max_value) {
        return max_value;
      }  else {
        const stockCheck = checkStockAvailability(newCount);
        if (!stockCheck.success) {
          props.onAction({
            action: 'SHOW_MESSAGE',
            params: {
              title: `Not enough items available. Only ${stockCheck.availableQuantity} left.`,
            },
          });
          return prevCount;
        }
        return newCount;
      }
    });
  };

  useEffect(() => {
    setPageState({ quantity: count === 0 ? 1 : count });
  }, [count, setPageState]);

  return (
    <Layout style={[styles.row, __appmakerCustomStyles?.container]}>
      <ThemeText
        size="md"
        fontFamily="medium"
        style={__appmakerCustomStyles?.title}>
        {title}
      </ThemeText>
      <StepperButton
        quantity={quantity}
        size={'lg'}
        min={min_value}
        max={max_value}
        increaseQuantity={() => handleQuantity(1)}
        decreaseQuantity={() => handleQuantity(-1)}
        extensionStyles={__appmakerCustomStyles?.stepper}
      />
    </Layout>
  );
};
const styles = StyleSheet.create({
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    marginBottom: 4,
    backgroundColor: '#fff',
  },
});

export default CounterBlock;