import React, { useEffect, useState } from 'react';
import { View, Keyboard, Platform } from 'react-native';

const KeyboardAwareSuggestions = ({ children }) => {
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    if (Platform.OS !== 'ios') {
      return;
    }

    const showListener = Keyboard.addListener('keyboardWillShow', (event) => {
      setKeyboardHeight(event.endCoordinates.height);
    });

    const hideListener = Keyboard.addListener('keyboardWillHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      showListener.remove();
      hideListener.remove();
    };
  }, []);

  if (Platform.OS !== 'ios') {
    return children;
  }

  return (
    <View style={{ flex: 1 }}>
      {children}
      <View style={{ height: keyboardHeight }} />
    </View>
  );
};

export default KeyboardAwareSuggestions;
