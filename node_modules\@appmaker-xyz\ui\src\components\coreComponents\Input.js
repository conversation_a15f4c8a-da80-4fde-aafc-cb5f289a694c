import React, { useState } from 'react';
import { View, TextInput, Pressable, I18nManager } from 'react-native';
import { styles as globalStyle } from '../../styles/index';
import { ThemeText } from '../atoms';
import Icon from 'react-native-vector-icons/FontAwesome';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { useTranslation } from 'react-i18next';

const Input = ({
  style,
  label,
  placeholder,
  helperText,
  value,
  onChange,
  icon,
  error,
  inputRef,
  secureTextEntry = false,
  textInputStyle,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { styles, theme } = useStyles(stylesheet);
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <View style={style}>
        <View style={[styles.textInput, isFocused && styles.focused]}>
          {label && (
            <ThemeText
              size="sm"
              color={theme.colors.lightText}
              style={styles.labelText}>
              {label}
            </ThemeText>
          )}
          <TextInput
            style={[styles.textInputStyle, textInputStyle]}
            placeholder={t(placeholder)}
            placeholderTextColor={theme.colors.placeholder}
            value={value}
            onChangeText={onChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            secureTextEntry={secureTextEntry && !showPassword}
            {...props}
            ref={inputRef}
          />
          {icon && icon}
          {secureTextEntry && (
            <Pressable
              style={styles.eyeIcon}
              onPress={() => setShowPassword(!showPassword)}>
              <Icon
                name={showPassword ? 'eye-slash' : 'eye'}
                size={14}
                color={theme.colors.lightText}
              />
            </Pressable>
          )}
        </View>
      </View>
      {helperText && (
        <ThemeText size="sm" color={theme.colors.lightText}>
          {helperText}
        </ThemeText>
      )}
      {error && (
        <ThemeText style={styles.errorText} size="sm" color={theme.colors.error}>
          {error}
        </ThemeText>
      )}
    </View>
  );
};
const stylesheet = createStyleSheet((theme) => ({
  container: {
    marginBottom: 12,
    flexGrow: 1,
  },
  errorText: {
    textAlign: 'left',
  },
  textInput: {
    borderWidth: 1,
    borderColor: theme.colors.separator,
    borderRadius: 6,
    paddingHorizontal: 4,
    fontSize: 16,
    fontFamily: globalStyle.fontFamily.regular,
  },
  labelText: {
    marginLeft: 3,
    marginTop: 4,
  },
  textInputStyle: {
    height: 40,
    fontFamily: globalStyle.fontFamily.regular,
    lineHeight: 20,
    color: theme.colors.text,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  focused: {
    borderColor: theme.colors.text,
  },
  eyeIcon: {
    position: 'absolute',
    end: 10,
    top: 8,
    padding: 4,
  },
}));

export default Input;
