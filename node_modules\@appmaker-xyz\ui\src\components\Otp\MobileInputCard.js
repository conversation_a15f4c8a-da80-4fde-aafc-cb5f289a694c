import React from 'react';
import { ThemeText, Layout } from '../atoms';
import MobileInputBox from './components/MobileInputBox';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { Button } from '../coreComponents';
import { isEmpty } from 'lodash';

const MobileInputCard = (props) => {
  const onPressHandle = props?.onPress;
  const onChangeText = props?.onChangeText;
  const onChangeFormattedText = props?.onChangeFormattedText;
  const defaultValue = props?.defaultValue;
  const loading = props?.isLoading;
  const countryCodes = props?.countryCodes ? props.countryCodes : null;
  const defaultCountryCode = props?.defaultCountryCode
    ? props.defaultCountryCode
    : 'IN';
  const { buttonColor = '#000', buttonTextColor = '#fff' } = props;
  const label = props?.label;

  const { styles, theme } = useStyles(stylesheet);

  return (
    <Layout style={styles.container}>
      <Layout style={styles.titleContainer}>
        {!isEmpty(label) ? (
          <ThemeText fontFamily="medium" size="md">
            {label}
          </ThemeText>
        ) : null}
      </Layout>
      <Layout style={styles.inputContainer}>
        <MobileInputBox
          defaultValue={defaultValue}
          defaultCode={defaultCountryCode}
          onChangeFormattedText={(number) => {
            onChangeFormattedText && onChangeFormattedText(number);
          }}
          onChangeText={(value) => {
            onChangeText && onChangeText(value);
          }}
          countryCodes={countryCodes}
        />
      </Layout>
      <Button
        title="Receive OTP"
        onPress={onPressHandle}
        isLoading={loading}
        color={theme.colors.primaryButtonBackground || buttonColor}
        activityIndicatorColor={
          theme.colors.primaryButtonText || buttonTextColor
        }
        textColor={theme.colors.primaryButtonText || buttonTextColor}
        fontType={'medium'}
        size={'md'}
        buttonStyle={styles.button}
      />
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    padding: 12,
  },
  titleContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  inputContainer: { height: 50 },
  button: {
    marginTop: 18,
  },
}));

export default MobileInputCard;
