import React, { useRef, useEffect, useState } from 'react';
import { TextInput, StyleSheet, Platform, Keyboard } from 'react-native';
import { ThemeText, Layout } from '../../atoms';
import RNOtpVerify from 'react-native-otp-verify';

const CustomOtpBar = ({ isOtpResent, showError, onFinalText, lengthInput }) => {
  const textInput = useRef();
  const [internalValue, setInternalValue] = useState('');
  const [inputKey, setInputKey] = useState(0); // Key to force re-render

  useEffect(() => {
    const focusInput = () => {
      if (textInput.current) {
        textInput.current.focus();
      }
    };

    setTimeout(focusInput, 100); // Delay to ensure focus after component mounts

    return () => {
      Keyboard.dismiss(); // Dismiss keyboard when component unmounts
    };
  }, [inputKey]); // Re-run effect when input<PERSON><PERSON> changes

  // Initialize OTP verification for Android
  useEffect(() => {
    if (Platform.OS === 'android') {
      startReadSMS();
    }

    return () => {
      // Cleanup OTP listener when component unmounts
      if (Platform.OS === 'android') {
        RNOtpVerify.removeListener();
      }
    };
  }, []);

  // Function to start reading SMS for OTP
  const startReadSMS = async () => {
    try {
      const hash = await RNOtpVerify.getHash();
      console.log('SMS hash:', hash);
      
      await RNOtpVerify.getOtp();
      await RNOtpVerify.addListener(otpHandler);
    } catch (err) {
      console.log('SMS read error:', err);
    }
  };

  // Handler for received OTP
  const otpHandler = (message) => {
    try {
      const otpMatch = /(\d{6})/g.exec(message);
      if (otpMatch && otpMatch[1]) {
        const otp = otpMatch[1];
        onChangeText(otp);
        RNOtpVerify.removeListener();
        Keyboard.dismiss();
      }
    } catch (error) {
      console.log('OTP parsing error:', error);
    }
  };

  const onChangeText = (val) => {
    setInternalValue(val);
    onFinalText(val);
  };

  useEffect(() => {
    if (isOtpResent) {
      setInternalValue('');
      setInputKey((prevKey) => prevKey + 1); // Update key to re-render input
      
      // Restart SMS reading if OTP is resent on Android
      if (Platform.OS === 'android') {
        RNOtpVerify.removeListener();
        startReadSMS();
      }
    }
  }, [isOtpResent]);

  return (
    <Layout>
      <TextInput
        key={inputKey} // Key to force re-render
        ref={textInput}
        onChangeText={onChangeText}
        style={styles.hiddenInput}
        value={internalValue}
        maxLength={lengthInput}
        placeholder={'0'}
        placeholderTextColor={'#f1f2f5'}
        secureTextEntry={true}
        caretHidden={true}
        returnKeyType={'done'}
        keyboardType={'numeric'}
      />
      <Layout style={styles.containerInput}>
        {Array(lengthInput)
          .fill()
          .map((item, index) => (
            <Layout
              key={index}
              style={[
                styles.otpCell,
                showError ? styles.errorCellOtp : styles.cellViewOtp,
              ]}>
              <ThemeText
                allowFontScaling={false}
                style={styles.cellTextOtp}
                onPress={() => {
                  textInput.current.focus();
                }}>
                {internalValue?.length > index ? internalValue[index] : ''}
              </ThemeText>
            </Layout>
          ))}
      </Layout>
    </Layout>
  );
};

const styles = StyleSheet.create({
  hiddenInput: {
    position: 'absolute',
    opacity: 0,
    width: 1,
    height: 1,
    width: '100%',  
    height: '100%',
    zIndex: 1,
  },
  containerInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  otpCell: {
    width: 38,
    height: 38,
    margin: 5,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    backgroundColor: '#fff',
    borderRadius: 6,
  },
  errorCellOtp: {
    borderColor: '#EF4444',
  },
  cellViewOtp: {
    borderColor: '#64748B',
  },
  cellTextOtp: {
    margin: 5,
    fontSize: 15,
    textAlign: 'center',
    color: '#212121',
  },
});

export default CustomOtpBar;
