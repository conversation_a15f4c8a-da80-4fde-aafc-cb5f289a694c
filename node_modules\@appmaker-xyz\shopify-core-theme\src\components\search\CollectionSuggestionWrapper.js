import React from 'react';
import CollectionSuggestions from './CollectionSuggestions';
import KeyboardAwareSuggestions from './KeyboardAwareSuggestions';
import { useSearchResult } from '@appmaker-xyz/shopify';

const CollectionSuggestionWrapper = (props) => {
  const { predictiveSearchResult: predictiveResults } = useSearchResult();
  return (
    <KeyboardAwareSuggestions>
      <CollectionSuggestions
        attributes={{
          title: 'Collections',
          collections: predictiveResults?.collections,
        }}
      />
    </KeyboardAwareSuggestions>
  );
};

export default CollectionSuggestionWrapper;
