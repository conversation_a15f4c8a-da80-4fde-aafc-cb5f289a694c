import React from 'react';
import { StyleSheet, TextInput, Pressable } from 'react-native';
import { ThemeText, Layout } from '@appmaker-xyz/ui';

const InputBox = (props) => {
  return (
    <Layout style={props.mainContainerStyle}>
      {props.titleText && (
        <ThemeText style={[styles.titleTextStyle, props.titleTextStyle]}>
          {props.titleText}
        </ThemeText>
      )}
      <Layout style={[styles.backgroundStyle, props.backgroundStyle]}>
        <TextInput
          {...props.options}
          style={[
            styles.inputStyle,
            props.inputStyle,
            props.disabled
              ? { borderColor: '#D9D9D9' }
              : { borderColor: '#5C5C5C' },
            props.showError ? { borderColor: '#BA3D3D' } : {},
          ]}
          defaultValue={props?.value || ''}
          placeholder={props.placeholder}
          multiline={false}
          editable={!props?.disabled}
          secureTextEntry={props.secure}
          onChangeText={props.onChangeText}
          keyboardType={props.keyboardType}
          maxLength={props.maxLength}
        />
        {props.isPassword ? (
          <Pressable
            onPress={props.onChangeSecure}
            style={{
              position: 'absolute',
              end: 15,
            }}>
            <Layout>
              <ThemeText style={styles.rightTextStyle}>
                {props.secure ? 'Show' : 'Hide'}
              </ThemeText>
            </Layout>
          </Pressable>
        ) : null}
      </Layout>
      {props.isErrorMessageEnabled && (
        <ThemeText style={styles.errorTextStyle}>
          {props.showError ? props.errorMessage : ''}
        </ThemeText>
      )}
    </Layout>
  );
};

const styles = StyleSheet.create({
  // Common
  backgroundStyle: {
    justifyContent: 'center',
  },
  inputStyle: {
    borderWidth: 1,
    textAlignVertical: 'center',
    color: '#5C5C5C',
    fontSize: 15,
    paddingVertical: 12,
    paddingHorizontal: 15,
    height: 46,
    borderRadius: 6,
  },
  rightTextStyle: {
    color: '#9D9D9D',
    fontSize: 12,
  },
  titleTextStyle: {
    color: '#494B4A',
    fontSize: 14,
    marginTop: 5,
    marginBottom: 2,
  },
  errorTextStyle: {
    color: '#BA3D3D',
    fontSize: 12,
  },
});

export default InputBox;
