import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Layout, ThemeText } from '@appmaker-xyz/ui';
import { Button, AppModal } from '../coreComponents';
import Icon from 'react-native-vector-icons/Feather';
import { testProps } from '@appmaker-xyz/core';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';

const OrderNotes = ({ onAction, attributes }) => {
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [orderNote, setOrderNote] = useState('');

  const { styles, theme } = useStyles(stylesheet);

  useEffect(() => {
    setOrderNote(attributes.orderNote);
  }, [attributes?.orderNote]);

  let toggle = () => setVisible(!visible);
  async function addNotes() {
    setLoading(true);
    const resp = await onAction({
      action: 'ADD_ORDER_NOTE',
      params: {
        orderNote,
        updateCartPageStateRequired: true,
      },
    });
    setLoading(false);
    toggle();
  }

  return (
    <Layout style={styles.container}>
      <Layout style={styles.orderNoteContainer}>
        <Layout style={styles.note}>
          <ThemeText size="md" fontFamily="medium" color={theme.colors.text}>
            Order Notes
          </ThemeText>
          {attributes?.orderNote ? (
            <ThemeText testId={'order-note'} size="sm" numberOfLines={3}>
              {attributes.orderNote}
            </ThemeText>
          ) : null}
        </Layout>
        <Button
          testId={orderNote ? 'edit-note-button' : 'add-note-button'}
          isOutline={true}
          color={theme.colors.lightText}
          size="sm"
          title={orderNote ? 'Edit Note' : 'Add Note'}
          onPress={() => toggle()}
        />
      </Layout>
      <AppModal
        isVisible={visible}
        onSwipeComplete={toggle}
        onBackButtonPress={toggle}
        backdropTransitionOutTiming={0}
        onBackdropPress={toggle}
        style={styles.modal}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={{}}>
          <Layout style={styles.modalBody}>
            <Layout style={styles.modalHeader}>
              <ThemeText size="md" color={theme.colors.text}>
                Order Notes
              </ThemeText>
              <Icon
                name="x"
                size={18}
                onPress={toggle}
                color={theme.colors.text}
              />
            </Layout>
            <Layout style={styles.modalContent}>
              <TextInput
                {...testProps('note-field')}
                value={orderNote}
                multiline={true}
                numberOfLines={4}
                placeholder="Add your order notes here"
                onChangeText={setOrderNote}
                style={styles.textArea}
                placeholderTextColor={theme.colors.placeholder}
              />
              <Button
                testId={'save-note-button'}
                title="Save Note"
                size="md"
                onPress={() => addNotes()}
                loading={loading}
              />
            </Layout>
          </Layout>
        </KeyboardAvoidingView>
      </AppModal>
    </Layout>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    marginBottom: 1,
  },
  orderNoteContainer: {
    padding: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: theme.colors.background,
  },
  note: {
    flex: 1,
    marginRight: 4,
  },
  modal: {
    justifyContent: 'flex-end',
    marginVertical: 0,
    marginHorizontal: 1,
  },
  modalBody: {
    backgroundColor: theme.colors.background,
    padding: 16,
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    borderTopStartRadius: 16,
    borderTopEndRadius: 16,
    flexDirection: 'column',
  },
  modalHeader: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  modalContent: {
    paddingVertical: 12,
    flexDirection: 'column',
    width: '100%',
  },
  textArea: {
    textAlignVertical: 'top',
    borderWidth: 0.5,
    borderColor: theme.colors.separator,
    borderRadius: 6,
    marginBottom: 6,
    padding: 6,
    margin: 1,
    color: theme.colors.text,
  },
}));

export default OrderNotes;
