import axios from 'axios';
const BASE_URL = 'https://reports.peachmode.com/shopify';
export async function requestOtp({ phone }) {
  try {
    const response = await axios.get(`${BASE_URL}/generate/otp`, {
      params: {
        phone,
      },
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}

export async function verifyOtp({ phone, otp }) {
  try {
    const response = await axios.post(`${BASE_URL}/verify/multipass`, {
      phone,
      otp,
    });
    return response.data;
  } catch (error) {
    return error.response.data;
  }
}
