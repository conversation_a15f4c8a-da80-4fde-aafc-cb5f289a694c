import React from 'react';
import WebView from 'react-native-webview';
import Modal from 'react-native-modal';
import { spacing } from '@appmaker-xyz/uikit/src/styles/index';
import { StyleSheet, View } from 'react-native';
import { useState } from 'react';
import ProgressBarWebview from './ProgressWebview';
import Icon from '@appmaker-xyz/uikit/Icons/Feather';
import { AppTouchable } from '@appmaker-xyz/uikit';

function CloseButton({ onPress }) {
  return (
    <AppTouchable
      onPress={onPress}
      style={{
        position: 'absolute',
        right: 12,
        top: 12,
        padding: 6,
        backgroundColor: '#00000066',
        zIndex: 50,
        borderRadius: 20,
      }}>
      <Icon name="x" size={18} color="#fff" />
    </AppTouchable>
  );
}
const LoadingBar = ({ color = '#3B78E7', percent, height = 3 }) => {
  const style = {
    backgroundColor: color,
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[styles.container, style]} />;
};

export default function ModalWebview({ visible, close, uri, onMessage }) {
  const [loadingState, setLoadingState] = useState({
    loading: true,
  });
  const _onLoadProgress = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      loading: true,
      percent: syntheticEvent?.nativeEvent?.progress,
    }));
  };

  const _onError = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      percent: 1,
      color: 'red',
    }));
  };

  const _onLoadStart = (syntheticEvent) => {
    setLoadingState((state) => ({
      ...state,
      loading: true,
      percent: 1,
    }));
  };

  const _onLoadEnd = (syntheticEvent) => {
    setTimeout(() => {
      setLoadingState((state) => ({
        ...state,
        percent: 100,
        loading: false,
      }));
    }, 3000);
  };
  return (
    <Modal
      testID={'modal'}
      isVisible={visible}
      onSwipeComplete={close}
      onBackButtonPress={close}
      backdropTransitionOutTiming={0}
      // onBackdropPress={close}
      style={styles.view}>
      <CloseButton onPress={close} />

      {loadingState?.loading && <LoadingBar percent={loadingState?.percent} />}
      <ProgressBarWebview uri={uri} onMessage={onMessage} />
      {/* <WebView
        source={{
          uri,
        }}
        style={{ width: '100%' }}
        onMessage={onMessage}
        onLoadStart={_onLoadStart}
        onLoadEnd={_onLoadEnd}
        onLoadProgress={_onLoadProgress}
        onError={_onError}
      /> */}
    </Modal>
  );
}
const styles = StyleSheet.create({
  view: {
    // borderRadius: spacing.small,
    overflow: 'hidden',
    margin: 0,
  },
  container: {
    position: 'absolute',
    zIndex: 10,
    top: 0,
    left: 0,
  },
});
