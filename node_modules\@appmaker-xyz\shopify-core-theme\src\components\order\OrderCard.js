import React from 'react';
import {
  Layout,
  ThemeText,
  AppImage,
  AppTouchable,
  Badge,
} from '@appmaker-xyz/ui';
import { useOrderItem, currencyHelper } from '@appmaker-xyz/shopify';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { appmaker } from '@appmaker-xyz/core';

const OrderCard = (props) => {
  const { order, orderId, status, openOrderDetail, hideOrderFulfillment } =
    useOrderItem(props);
  const { styles, theme } = useStyles(stylesheet);

  let orderStatusText = appmaker.applyFilters('custom-order-status', status);

  function cardStatus() {
    switch (status) {
      case 'SCHEDULED':
        return theme.colors.primaryButtonBackground;
      case 'UNFULFILLED':
        return theme.colors.warning;
      case 'FULFILLED':
        return theme.colors.success;
      case 'ON_HOLD':
        return theme.colors.warning;
      case 'PARTIALLY_FULFILLED':
        return theme.colors.warning;
      default:
        return theme.colors.secondaryButtonBackground;
    }
  }

  return (
    <AppTouchable style={styles.container} onPress={openOrderDetail}>
      <Layout style={styles.header}>
        <ThemeText size="md" fontFamily="medium">
          ORDER ID: {orderId}
        </ThemeText>
        {hideOrderFulfillment ? null : (
          <Badge
            text={orderStatusText}
            color={cardStatus()}
            customStyles={{
              textStyle: {
                fontSize: 13,
                fontFamily: theme.fontFamily.medium,
              },
            }}
          />
        )}
      </Layout>
      <Layout style={styles.contentContainer}>
        {order.lineItems.edges.map((item) => {
          const orderItem = item?.node;
          const quantity = orderItem?.quantity;
          const orderItemTotalAmount = parseFloat(
            orderItem?.discountedTotalPrice?.amount / quantity,
          );
          const orderItemPrice = currencyHelper(
            orderItemTotalAmount,
            orderItem?.discountedTotalPrice?.currencyCode,
          );
          const orderItemVariant = orderItem?.variant;
          const image = orderItemVariant?.image?.url;
          const variantTitle =
            orderItemVariant?.title &&
            orderItemVariant?.title !== 'Default Title'
              ? orderItemVariant?.title
              : null;
          return (
            <Layout style={styles.lineItem}>
              <AppImage uri={image} style={styles.variantImage} />
              <Layout style={styles.itemContent}>
                <ThemeText fontFamily="medium">{orderItem?.title}</ThemeText>
                <Layout style={styles.itemDetails}>
                  {variantTitle ? (
                    <ThemeText style={styles.variationText} size="sm">
                      {variantTitle}
                    </ThemeText>
                  ) : (
                    <Layout />
                  )}
                  <ThemeText fontFamily="bold" size="sm">
                    {quantity} x {orderItemPrice}
                  </ThemeText>
                </Layout>
              </Layout>
            </Layout>
          );
        })}
      </Layout>
    </AppTouchable>
  );
};

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.background,
    marginHorizontal: 12,
    marginTop: 10,
    marginBottom: 2,
    padding: 12,
  },
  contentContainer: {},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomColor: theme.colors.separator,
    borderBottomWidth: 1,
    paddingBottom: 8,
  },
  lineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    flex: 1,
  },
  variantImage: {
    width: 40,
    height: 50,
    marginRight: 8,
    backgroundColor: theme.colors.lightBackground,
  },
  itemContent: {
    flexGrow: 1,
    flexShrink: 1,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2,
  },
  variationText: {
    backgroundColor: theme.colors.lightBackground,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderTopColor: theme.colors.lightBackground,
    borderTopWidth: 1,
    paddingTop: 8,
    marginTop: 8,
  },
  orderButton: {
    flexGrow: 1,
    marginRight: 4,
  },
  trackButton: {
    flexGrow: 1,
    marginLeft: 4,
    borderWidth: 1,
  },
}));

export default OrderCard;
