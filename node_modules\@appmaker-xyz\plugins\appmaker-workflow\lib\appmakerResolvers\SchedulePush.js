import AsyncStorage from '@react-native-community/async-storage';
import notifee, { TriggerType } from '@notifee/react-native';
import dayjs from 'dayjs';

export class SchedulePush {
  async exec(params, context) {
    // notifee.
    // Create a trigger notification
    const { title, body, triggerInSeconds, pushAction, pushLocalIdKey } = params;
    const day = dayjs().add(triggerInSeconds, 'second');
    const date = day.toDate();
    // console.log(day.fromNow());
    // sleep 5 seconds
    const trigger = {
      type: TriggerType.TIMESTAMP,
      timestamp: date.getTime(), // fire at 11:10am (10 minutes before meeting)
    };
    const currentId = await AsyncStorage.getItem(pushLocalIdKey);
    if (currentId !== null) {
      await notifee.cancelNotification(currentId);
    }
    const channelId = await notifee.createChannel({
      id: 'default',
      name: 'Default Channel',
    });
    const uuid = await notifee.createTriggerNotification(
      {
        title,
        body,
        data: {
          action: pushAction,
        },
        android: {
          channelId,
          pressAction: {
            id: 'open-action',
            launchActivity: 'default',
          },
        },
      },
      trigger
    );
    await AsyncStorage.setItem(pushLocalIdKey, uuid);
    return { localPushId: uuid, params };
  }
}
