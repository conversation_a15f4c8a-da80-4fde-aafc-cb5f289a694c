import React from 'react';
import { ActivityIndicator, StyleSheet, FlatList } from 'react-native';
import { Layout } from '@appmaker-xyz/ui';
import { FlashList } from '@shopify/flash-list';
import { useProducts } from '../../../hooks/product/useProducts';

type CollectionQueryInput = {
  handle?: string;
  id?: string;
};
type Search = {
  query: string;
};
type Referrer = {
  type?: string;
  title?: string;
  name?: string;
};
type Props = {
  collectionQuery: CollectionQueryInput;
  appliedFilters?: any;
  appliedSort?: any;
  BlockItemRender: React.FC<any>;
  onAction: () => any;
  search?: Search;
  beforeProductRender?: (items: any[]) => any[];
  loadingComponent?: () => React.ReactElement;
  productIds?: string[];
  horizontal?: boolean;
  limit?: number;
  disablePagination?: boolean;
  productRecommendationId?: string;
  surface?: string;
  referrer?: Referrer;
  flashListProps?: any;
  itemsDisabled?: boolean;
  useFlatlist?: boolean;
  customProductGridBlock?: string;
  blockAttributes?: any;
  numColumns?: number;
  layoutProps?: any;
  loadingLayout?: string;
  additionalQueryParams?: any;
  hideWhenEmpty?: boolean;
};
const productGridBlockName = 'appmaker/product-grid-item';

const ProductList: React.FC<Props> = (props) => {
  const {
    collectionQuery,
    BlockItemRender,
    onAction,
    appliedFilters,
    appliedSort,
    search,
    beforeProductRender,
    loadingComponent,
    productIds,
    horizontal = false,
    limit,
    disablePagination = false,
    productRecommendationId,
    surface,
    referrer,
    itemsDisabled,
    useFlatlist,
    customProductGridBlock,
    blockAttributes,
    numColumns,
    layoutProps,
    loadingLayout = 'product-grid',
    additionalQueryParams = {},
    hideWhenEmpty = false,
  } = props;
  // TODO: seperate based on props
  const query = {
    ...(collectionQuery?.id && {collectionId: collectionQuery?.id}),
    ...(collectionQuery?.handle && {collectionHandle: collectionQuery?.handle}),
    ...(search?.query && {searchQuery: search?.query}),
    ...(productIds && {productIds: productIds,}),
    ...(productRecommendationId && {productRecommendationId: productRecommendationId})
  };
  let flashListProps = props?.flashListProps || {};
  const {
    data,
    isLoading,
    isError,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
    productList,
    isFetching,
  } = useProducts({ query, filters: appliedFilters, sort: appliedSort, limit, surface, ...additionalQueryParams });
  const loadNext = () => {
    if (hasNextPage) {
      fetchNextPage();
    }
  };
  let productListData = typeof beforeProductRender === 'function' ? beforeProductRender(productList) : productList;

  if (horizontal) {
    flashListProps = {
      ...flashListProps,
      horizontal: true,
    };
  } else {
    flashListProps = {
      ...flashListProps,
      numColumns: 2,
    };
  }
  if (!disablePagination) {
    flashListProps = {
      ...flashListProps,
      onEndReached: loadNext,
      onEndReachedThreshold: 0.5,
      ListFooterComponent: () =>
        !isLoading && isFetching ? (
          <ActivityIndicator size="small" color="#000000" />
        ) : null,
    };
  }

  const key = `custom-collection-${
    collectionQuery?.id ||
    collectionQuery?.handle ||
    search?.query ||
    productRecommendationId ||
    (productIds?.length > 0 && productIds[0]) ||
    ''
  }`;
  const finalNumColumns = numColumns || blockAttributes?.numColumns || blockAttributes?.layout === 'grid' && 2;
  const renderItem = ({ item, extraData, index }) => (
    <BlockItemRender
      extraData={extraData}
      blockData={item}
      onAction={onAction}
      blocksViewItemIndex={index}
      BlockItemRender={BlockItemRender}
      block={{
        name: blockAttributes?.customProductGridItemName || customProductGridBlock || productGridBlockName,
        attributes: {
          __experimentalDisableListItemParser: true,
          gridViewListing: !horizontal,
          horizontal: horizontal,
          surface,
          referrer,
          hasPages: !disablePagination,
          flashListComponent: true,
          productRecommendationId,
          productIds,
          limit,
          itemDisabled: itemsDisabled,
          numColumns: finalNumColumns,
          ...blockAttributes,
        },
      }}
    />
  );

  if (productListData?.length === 0) {
    if (hideWhenEmpty) {
      return null;
    }
    return (
      <BlockItemRender
        onAction={onAction}
        BlockItemRender={BlockItemRender}
        block={{
          name: 'appmaker/error-block',
          attributes: {
            empty: true,
            emptyText: 'No Items',
          },
        }}
      />
    );
  }

  return (
    <Layout
      loading={isLoading}
      loadingLayout={loadingLayout}
      loadingComponent={loadingComponent}
      style={styles?.container}
      {...layoutProps}>
      {useFlatlist ? (
        <FlatList
          key={key}
          {...flashListProps}
          data={productListData}
          bounces={false}
          contentContainerStyle={styles.flatListContainer}
          estimatedItemSize={400}
          renderItem={renderItem}
        />
      ) : (
        <FlashList
          key={key}
          {...flashListProps}
          data={productListData}
          bounces={false}
          contentContainerStyle={styles.flatListContainer}
          estimatedItemSize={400}
          renderItem={renderItem}
        />
      )}
    </Layout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  flatListContainer: {
    paddingHorizontal: 5,
  },
});

export default ProductList;
