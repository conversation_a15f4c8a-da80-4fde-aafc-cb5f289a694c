import React, { useState, useEffect, useRef } from 'react';
import { WebView } from 'react-native-webview';
import { check, request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import { useNavigation } from '@react-navigation/native';
import { HeaderBackButton } from '@react-navigation/elements';
import {
  View,
  Text,
  StyleSheet,
  Platform,
  Dimensions,
  BackHandler,
} from 'react-native';
import { Layout, LayoutIcon } from '@appmaker-xyz/ui';
import AutoHeightWebView from './AutoHeightWebView.js';
import {
  webViewCanHandleCurrentUrl,
  webViewHandleOnMessage,
} from '@appmaker-xyz/react-native';
import {
  emitEvent,
  appSettings,
  usePluginStore,
  applyFilters,
} from '@appmaker-xyz/core';
import { isArray } from 'lodash';

let { width, height } = Dimensions.get('window');

function isExitFrame(url, exitFrameRegexList) {
  const list = Object.values(exitFrameRegexList);
  return list?.some((regex) => new RegExp(regex).test(url));
}

const LoadingBar = ({ color = '#3B78E7', percent, height = 3 }) => {
  console.log(`${parseInt(percent) * 100}%`);
  const style = {
    backgroundColor: color,
    width: `${percent * 100}%`,
    height,
  };
  return <View style={[style]} />;
};

const AppmakerWebView = ({ attributes = {}, onAction, ...props }) => {
  const [progress, setProgress] = useState(0);
  const [isLoaded, setLoaded] = useState(false);
  const {
    navigationFilter,
    urlListener,
    permissionsRequired = [],
    source,
    deniedPatterns = [],
    getOnBackButtonPressFunction = false,
    originWhitelist = ['*'],
    exitFrameRegexList = [],
  } = attributes;
  let finalOriginWhitelist = originWhitelist;
  if (!isArray(finalOriginWhitelist)) {
    finalOriginWhitelist = Object.values(finalOriginWhitelist);
  }
  const deniedPatternsArray =
    deniedPatterns && Array.isArray(deniedPatterns)
      ? deniedPatterns
      : Object.values(deniedPatterns);
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');
  let [webviewSource, setWebviewSource] = useState({});
  const [redirectLoading, setRedirectLoading] = useState(true);
  let [isGobackDone, setIsGobackDone] = useState(false);
  const webview = useRef();
  useEffect(() => {
    urlListener && urlListener(currentUrl, onAction, webview);
  }, [currentUrl, urlListener, onAction]);

  const navigation = useNavigation();
  navigation &&
    getOnBackButtonPressFunction &&
    navigation.setOptions({
      headerLeft: (prop) => {
        const appBackButtonName = applyFilters('app-back-button-icon', false);

        if (attributes.backButtonIconName || appBackButtonName) {
          return (
            <LayoutIcon
              clientId="back-button"
              attributes={{
                svgXml: attributes.svgXml || appBackButtonName.svgXml,
                svgIcon: attributes.svgIcon || appBackButtonName.svgIcon,
                iconName:
                  attributes.backButtonIconName || appBackButtonName.name,
                iconColor: appSettings.getOption('toolbar_text_color'),
              }}
              onPress={getOnBackButtonPressFunction({
                webview,
                canGoBack,
                canGoForward,
                currentUrl,
                onAction,
                navigation,
              })}
            />
          );
        }
        return (
          <HeaderBackButton
            {...prop}
            onPress={getOnBackButtonPressFunction({
              webview,
              canGoBack,
              canGoForward,
              currentUrl,
              onAction,
              navigation,
            })}
          />
        );
      },
    });

  useEffect(() => {
    if (attributes.redirectURL) {
      onAction(attributes.redirectAction)
        .then((url) => {
          setWebviewSource({ uri: url });
          setRedirectLoading(false);
        })
        .catch((error) => {
          setWebviewSource(source);
          setRedirectLoading(false);
        });
    } else {
      setRedirectLoading(false);
      if (source && typeof source !== 'object') {
        webviewSource[source] = attributes[source];
        setWebviewSource(webviewSource);
      } else {
        setWebviewSource(source);
      }
      if (source === 'html') {
        webviewSource.html = `${attributes.preHtml ? attributes.preHtml : ''}${
          webviewSource.html
        }${attributes.postHtml ? attributes.postHtml : ''}`;
        setWebviewSource(webviewSource);
      }
    }
  }, [attributes.source]);
  useEffect(() => {
    emitEvent('ensure-permissions', Object.values(permissionsRequired || []), {
      url: source?.uri,
    });
  }, [source, permissionsRequired]);

  const [loading, setLoading] = useState(true);
  let extraProps = {};
  if (props.url) {
    extraProps = {
      mediaPlaybackRequiresUserAction: true,
      startInLoadingState: true,
    };
  }

  if (Platform.version > 16) {
    extraProps.mediaPlaybackRequiresUserAction = true;
  }

  const appSettingsPlugin = usePluginStore(
    (state) => state?.plugins['app-settings']?.settings,
  );

  const permissionSettings = usePluginStore(
    (state) => state?.plugins['app-permissions']?.settings,
  );

  const LocationPermissionEnabled = permissionSettings?.permissions?.find(
    function (obj) {
      return obj.permission == 'location';
    },
  );

  if (LocationPermissionEnabled && attributes?.permissionsRequired) {
    const hasLocationPermission = Object.values(attributes.permissionsRequired).some(
      permission => permission.value === 'location'
    );
    if (hasLocationPermission) {
    extraProps.geolocationEnabled = true;
    const androidPermission = PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
    const iosPermission = PERMISSIONS.IOS.LOCATION_WHEN_IN_USE;
    check(Platform.OS === 'ios' ? iosPermission : androidPermission).then(
      (result) => {
        if (result != RESULTS.GRANTED) {
          request(
            Platform.OS === 'ios' ? iosPermission : androidPermission,
          ).then((res) => {
            if (res === RESULTS.GRANTED) {
            } else {
            }
            // …
          });
        }
      },
    );
  }
  }

  if (appSettingsPlugin?.disable_webview_hardaware_accelaration == true) {
    extraProps.androidHardwareAccelerationDisabled = true; // this is added to resolve a crash happened in tab switching in some devices (libc.so)
  }
  let userAgentProps = {};
  if (attributes?.userAgent) {
    userAgentProps = {
      userAgent: attributes.userAgent,
    };
  }
  // if (Config.USER_AGENT_CHANGE == 1 && Platform.OS == 'android') {
  // userAgentProps = {
  //   userAgent: 'gumstack:embed:android',
  // };
  // }

  const backButtonHandler = () => {
    if (getOnBackButtonPressFunction) {
      return getOnBackButtonPressFunction({
        webview: webview,
        canGoBack: canGoBack,
        canGoForward: canGoForward,
        currentUrl: currentUrl,
        onAction,
        navigation,
      })();
    } else {
      if (canGoBack) {
        webview.current.goBack();
        return true; // prevent default behavior (exit app)
      }
      return false;
    }
  };
  useEffect(() => {
    const handleBlur = () => {
      BackHandler.removeEventListener('hardwareBackPress', backButtonHandler);
    };
    const handleFocus = () => {
      // Prevent duplicate listeners
      BackHandler.removeEventListener('hardwareBackPress', backButtonHandler);
      BackHandler.addEventListener('hardwareBackPress', backButtonHandler);
    };
    const unsubscribeFocus = navigation.addListener('focus', handleFocus);
    const unsubscribeBlur = navigation.addListener('blur', handleBlur);
    BackHandler.addEventListener('hardwareBackPress', backButtonHandler);
    return () => {
      BackHandler.removeEventListener('hardwareBackPress', backButtonHandler);
      unsubscribeFocus();
      unsubscribeBlur();
    };
  }, [
    canGoBack,
    canGoForward,
    currentUrl,
    webview,
    getOnBackButtonPressFunction,
  ]);
  // let containerStyle = {height: attributes.height};
  let containerStyle = attributes.height && {
    height: Number.parseInt(attributes.height),
  };
  const LoadingView = () => (
    <View style={styles.loadingContainerStyle}>
      <Text>Loading.....</Text>
      {/* <ActivityIndicator color="#212121" size="large" /> */}
      {/* <Layout loading={true} loadingLayout={loadingLayout} /> */}
    </View>
  );

  // if (redirectLoading) {
  //   return LoadingView();
  // }
  if (attributes.autoHeight) {
    return (
      <AutoHeightWebView
        style={{ flex: 1, height: 1000 }}
        source={webviewSource}
        // startInLoadingState={true}
        defaultHeight={3000}
        // onLoadEnd={this.onWebViewLoadEnd}
        // onShouldStartLoadWithRequest={this.onShouldStartLoadWithRequest}
        // onNavigationStateChange={this.onShouldStartLoadWithRequest}
      />
    );
  }
  const appmakerWebScript = `
  window.appmakerWebview = { platform: "${Platform.OS}" }
  window.appmaker = {};
  window.appmaker.postMessage = function (message) {
    try {
      message = message || {};
      window.ReactNativeWebView.postMessage(JSON.stringify(message));
    } catch (e) {
      console.error('Error in postMessage', e);
    }
  }
  window.appmaker.appmakerAction = function (action, params) {
    window.appmaker.postMessage({
      appmakerAction: {
        action,
        params,
      },
    });
  };  
  ${attributes?.injectedJavaScript ? attributes.injectedJavaScript : ''}
  true; // note: this is required, or you'll sometimes get silent failures
`;
  const openUrl = ({ url }) => {
    onAction({
      action: 'OPEN_URL',
      params: {
        url,
      },
    });
    return false;
  };
  function onShouldStartLoadWithRequest(navState) {
    if (
      webViewCanHandleCurrentUrl &&
      webViewCanHandleCurrentUrl(navState, onAction, {
        deniedPatterns: deniedPatternsArray,
      },
      { webViewRef: webview || undefined},
    )
    ) {
      return false;
    }
    setCanGoBack(navState.canGoBack);
    setCanGoForward(navState.canGoForward);
    setCurrentUrl(navState.url);
    navigationFilter && navigationFilter(navState, onAction);
    return true;
  }
  return (
    <Layout style={[{ flex: 1 }, containerStyle]} loading={false}>
      {!isLoaded ? <LoadingBar percent={progress} color="#ff8300" /> : null}
      {webviewSource.uri || webviewSource.html ? (
        <WebView
          onLoadProgress={({ nativeEvent }) =>
            setProgress(nativeEvent.progress)
          }
          onLoadEnd={() => setLoaded(true)}
          // onLoadStart
          showsVerticalScrollIndicator={false}
          ref={webview}
          injectedJavaScript={
            attributes?.injectedJavaScript || appmakerWebScript
          }
          // injectJavaScript={'alert("hello ")'}

          {...extraProps}
          {...userAgentProps}
          useWebKit={true}
          domStorageEnabled={true}
          originWhitelist={finalOriginWhitelist}
          automaticallyAdjustContentInsets={true}
          style={[styles.containerWebView, { opacity: 0.99 }]}
          allowsInlineMediaPlayback={true}
          sharedCookiesEnabled={true}
          // onShouldStartLoadWithRequest={this.onShouldStartLoadWithRequest}
          // onNavigationStateChange={this.onShouldStartLoadWithRequest}
          source={webviewSource}
          onMessage={(event) => {
            webViewHandleOnMessage(event, {
              onAction,
              url: currentUrl,
            });
          }}
          javaScriptEnabled={true}
          onLoadStart={() => {
            setLoading(true);
            setLoaded(false);
          }}
          onShouldStartLoadWithRequest={onShouldStartLoadWithRequest}
          onNavigationStateChange={(navState) => {
            if (typeof navState.url === 'string') {
              setCanGoBack(navState.canGoBack);
              setCanGoForward(navState.canGoForward);
              setCurrentUrl(navState.url);
              const isExitFrameUrl = isExitFrame(
                navState.url,
                exitFrameRegexList,
              );
              if (isExitFrameUrl && !isGobackDone) {
                setIsGobackDone(true);
                onAction({
                  action: 'GO_BACK',
                  params: {},
                });
              }
              navigationFilter && navigationFilter(navState, onAction);
            }
          }}
          onLoad={() => {
            setLoading(false);
          }}
          cacheEnabled={true}
          // cacheMode="LOAD_CACHE_ELSE_NETWORK"
          startInLoadingState={true}
          // renderLoading={LoadingView}
        />
      ) : null}
      {/* {loading && <LoadingView />} */}
    </Layout>
  );
};

const styles = StyleSheet.create({
  containerWebView: {
    flex: 1,
  },
  loadingContainerStyle: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: width,
    height: height,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'white',
  },
});
export default AppmakerWebView;
