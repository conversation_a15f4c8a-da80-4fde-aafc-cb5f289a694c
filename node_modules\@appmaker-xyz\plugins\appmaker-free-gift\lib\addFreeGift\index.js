import { parse } from '@appmaker-xyz/core';
import {
  shopifyIdHelper,
  productsStore,
  hasVariantInCart,
} from '@appmaker-xyz/shopify';
import getConditionalCartSum from './getConditionalCartSum';
import checkProductIsAvailable from './checkProductIsAvailable';

// Logger for debugging
function log(...args) {
  // console.log('free-gift', ...args);
  if (args.debugLog) {
    // console.log('free-gift', ...args);
  }
}

// Merge freeGiftProduct with backupProducts
function getProductWithBackupProducts(product) {
  const backupProducts = product.backup_products || [];
  return [product, ...backupProducts];
}

// freeGiftProducts is sorted of freeGiftProduct + backupProducts
// returns available freeGiftVariantId or false
async function getAvailableVariant({
  freeGiftProducts,
  freeGiftDependencies,
  input,
}) {
  for (const product of freeGiftProducts) {
    const productId = shopifyIdHelper(product.product_gid);
    const productData = await productsStore.getProduct(productId);
    let availableVariantId = await checkProductIsAvailable({
      input,
      productData,
      variantId: product.variant_gid,
      dependencies: freeGiftDependencies,
    });
    if (availableVariantId) {
      return { availableVariantId, productData };
    }
  }
  return false;
}
export function cleanString(value) {
  const cleanedValue = value?.replace?.(/\s+/g, '')?.toLowerCase();
  return cleanedValue;
}

export function hasAttributeInCart(
  { currentCart, lineItemsToAdd = [], lineItemsToUpdate = [] },
  { attribute },
) {
  try {
    let currentLineItem = currentCart?.lineItems?.edges?.find((item) =>
      item?.node?.customAttributes?.find(
        (attr) =>
          attr?.key === attribute?.key && attr?.value === attribute?.value,
      ),
    );
    if (!currentLineItem && lineItemsToAdd.length > 0) {
      // Check if the attribute is already in lineItemsToAdd
      currentLineItem = lineItemsToAdd.find((item) =>
        item?.node?.customAttributes?.find(
          (attr) =>
            attr?.key === attribute?.key && attr?.value === attribute?.value,
        ),
      );
    }
    // If quantity is 0 in lineItemsToUpdate, then return as it doesn't exist in cart
    if (
      currentLineItem &&
      lineItemsToUpdate.length > 0 &&
      lineItemsToUpdate.find(
        (item) =>
          item?.node?.customAttributes?.find(
            (attr) =>
              attr?.key === attribute?.key && attr?.value === attribute?.value,
          ) && item.quantity === 0,
      )
    ) {
      currentLineItem = false;
    }
    return currentLineItem;
  } catch (e) {
    return false;
  }
}

async function getAllFreeGifts({
  input,
  freeGiftConfig,
  freeGiftDependencies,
}) {
  try {
    const freeGiftsToAdd = [];
    const existingFreeGifts = [];
    const existingFreeGiftVariantIds = [];
    const nextFreeGifts = [];

    // Select only enabled freeGifts
    const freeGifts = freeGiftConfig
      .filter((gift) => gift.status)
      .map((gift) => {
        const { sum, qty } = getConditionalCartSum({
          input,
          cartSumCondition: gift?.cart_value_condition,
          collections: gift?.collections,
          dependencies: freeGiftDependencies,
          discountCodeTypesToExclude:
            gift?.cart_value_include_discount_code_types,
          useCompareAtPrice: gift?.use_compare_at_price,
        });
        return {
          ...gift,
          cartTotal: sum,
          totalQty: qty,
        };
      })
      .sort((a, b) => a.cart_value_amount - b.cart_value_amount);

    // Loop through freeGifts
    for (const gift of freeGifts) {
      log('gift-loop', gift);

      const {
        products,
        cart_value_amount,
        cartTotal,
        type,
        totalQty,
        cart_value_include_discount_code_types,
        coupon_codes = [],
      } = gift;
      const cart_value_required = parseFloat(cart_value_amount);

      // Loop through free Gift Products
      for (const product of products) {
        const productWithBackupProducts = getProductWithBackupProducts(product);

        log('productWithBackupProducts', productWithBackupProducts);
        // Get available variant
        let { availableVariantId, productData } = await getAvailableVariant({
          freeGiftProducts: productWithBackupProducts,
          freeGiftDependencies,
          input,
        });

        log('availableVariantId', availableVariantId);

        let freeGiftConditionMet = false;

        if (type === 'cart_value') {
          freeGiftConditionMet = cartTotal >= cart_value_required;
        } else if (type === 'cart_quantity') {
          freeGiftConditionMet = totalQty >= cart_value_required;
        } else if (type === 'coupon_code') {
          const discountCodes =
            input?.currentCart?.discountApplications?.edges || [];
          freeGiftConditionMet = discountCodes.find((code) => {
            const { node } = code;
            const { code: codeValue } = node;
            /**
             * coupon_codes [{"__isNew__": true, "label": "MOBILE_APP", "value": "MOBILE_APP"}]
             */
            return coupon_codes.find((coupon) => coupon.value === codeValue);
          })
            ? true
            : false;
        } else if (type === 'always') {
          freeGiftConditionMet = true;
        }

        // Check if cart total is greater than or equal to cart_value_required
        if (freeGiftConditionMet) {
          log('free gift condition met', {
            cartTotal,
            totalQty,
            type,
            cart_value_required,
          });

          log('freeGiftIndex', products);

          const thisAttribute = {
            key: 'gift_id',
            value: `${cleanString(gift?.name)}`,
          };
          // Check if product is available
          const attributeCondition =
            availableVariantId &&
            !hasAttributeInCart(input, {
              attribute: thisAttribute,
            });
          if (
            (availableVariantId &&
              !hasVariantInCart(input, availableVariantId)) ||
            attributeCondition
          ) {
            log('Adding free gift', availableVariantId);
            // Add free gift to cart
            freeGiftsToAdd.push({
              variantId: availableVariantId,
              quantity: 1,
              customAttributes: [
                {
                  key: 'appmaker_free_gift',
                  value: 'Free Gift',
                },
                {
                  key: 'gift_id',
                  value: `${cleanString(gift?.name)}`,
                },
                ...(gift?.attributes || []),
              ],
            });
          } else if (availableVariantId) {
            log('Free gift already in cart', availableVariantId);
            existingFreeGifts.push({
              variantId: availableVariantId,
              product: productData,
              gift: gift,
            });
            existingFreeGiftVariantIds.push(availableVariantId);
          } else {
            log('Not adding as all gift unavailable', availableVariantId);
          }
        } else if (!freeGiftConditionMet) {
          log('free gift condition not met', {
            cartTotal,
            totalQty,
            type,
            cart_value_required,
          });

          if (availableVariantId && productData) {
            let difference = 0;
            if (type === 'cart_value') {
              difference = cart_value_required - cartTotal;
            } else if (type === 'cart_quantity') {
              difference = cart_value_required - totalQty;
            }

            // const difference = gift.cart_value_amount - cartTotal;

            const cartMessage = parse({
              template: {
                message: gift.pre_offer_apply_cart_message,
              },
              data: {
                difference: difference.toFixed(0),
                type,
                currentCart: input.currentCart,
                freeGift: gift,
              },
            });

            log('cartMessage', cartMessage);
            log('product', product);

            nextFreeGifts.push({
              conditionType: type,
              difference: difference.toFixed(0),
              message: cartMessage?.message,
              product: productData,
            });
          }
        }
      }
    }
    return {
      freeGiftsToAdd,
      existingFreeGifts,
      existingFreeGiftVariantIds,
      nextFreeGifts,
    };
  } catch (error) {
    console.error(error);
  }
}

export function returnAllFreeGifts(freeGiftConfig, debugLog = true) {
  return async (freeGifts, dependencies) => {
    // console.log('returnAllFreeGifts', {
    //   freeGifts,
    //   dependencies,
    // });

    const freeGiftDependencies = {
      ...dependencies,
      freeGiftConfig,
      debugLog,
      log,
    };

    const input = {
      currentCart: dependencies.cart,
      lineItemsToAdd: [],
      lineItemsToRemove: [],
      lineItemsToUpdate: [],
    };
    // console.log('input-1234', input);

    return await getAllFreeGifts({
      input: input,
      freeGiftConfig,
      freeGiftDependencies,
    });
  };
}

// Get variants to remove from cart as free gift not eligible
function getFreeGiftsToRemove({
  currentCartItems,
  existingFreeGifts,
  existingGift,
}) {
  log(
    'getFreeGiftsToRemove',
    JSON.stringify({
      currentCartItems,
      existingFreeGifts,
    }),
  );
  const freeGiftsInCartToRemove = currentCartItems.filter((item) => {
    const { customAttributes, variant } = item.node;

    // Check if free gift
    const isFreeGift = customAttributes.find(
      (attribute) => attribute.key === 'appmaker_free_gift',
    );

    const gid = customAttributes.find(
      (attribute) => attribute.key === 'gift_id',
    )?.value;

    log('isFreeGift', isFreeGift);

    // Check if free gift is an existing free gift
    // const isExistingFreeGift = existingFreeGifts.some(
    //   (existingFreeGiftItem) => {
    //     return existingFreeGiftItem === variant.id;
    //   },
    // );
    const isExistingFreeGiftV2 = existingGift?.some(
      (gift) =>
        gift.variantId === variant.id && gid === cleanString(gift?.gift?.name),
    );

    log('isExistingFreeGift', isExistingFreeGiftV2);

    // Remove free gift if it is not an existing free gift
    return isFreeGift && !isExistingFreeGiftV2;
  });
  log('freeGiftsInCartToRemove', freeGiftsInCartToRemove);

  // Remove all non-elgible free gifts
  const freeGiftToRemove = freeGiftsInCartToRemove.map((item) => {
    const { id: lineItemId } = item.node;
    return lineItemId;
  });
  return freeGiftToRemove;
}

export function addFreeGift(freeGiftConfig, debugLog = true) {
  return async (input, dependencies) => {
    input.currentCart = input?.currentCart || dependencies.cart;
    log('config', { freeGiftConfig, input });
    const freeGiftDependencies = {
      log,
      ...dependencies,
    };

    log('config', { freeGiftConfig, input });

    const {
      freeGiftsToAdd,
      existingFreeGiftVariantIds,
      nextFreeGifts,
      existingFreeGifts,
    } = await getAllFreeGifts({
      freeGiftConfig,
      input,
      freeGiftDependencies,
    });

    log('freeGiftsToAdd', freeGiftsToAdd);
    log('existingFreeGifts', existingFreeGiftVariantIds);
    log('nextFreeGifts', nextFreeGifts);

    const currentCartItems = input?.currentCart?.lineItems?.edges || [];
    const freeGiftsInCartToRemove = getFreeGiftsToRemove({
      currentCartItems,
      existingFreeGifts: existingFreeGiftVariantIds,
      existingGift: existingFreeGifts,
    });

    // Merge freeGiftsToAdd and lineItemsToUpdate to input
    input.lineItemsToAdd = input.lineItemsToAdd
      ? [...input.lineItemsToAdd, ...freeGiftsToAdd]
      : freeGiftsToAdd;

    // Merge freeGiftsInCartToRemove and lineItemsToUpdate to input
    input.lineItemsToRemove = input.lineItemsToRemove
      ? [...input.lineItemsToRemove, ...freeGiftsInCartToRemove]
      : freeGiftsInCartToRemove;

    return input;
  };
}
