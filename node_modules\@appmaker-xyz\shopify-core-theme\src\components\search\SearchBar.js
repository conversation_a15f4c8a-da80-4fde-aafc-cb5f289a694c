import React, { useEffect, useRef } from 'react';
import { TextInput, View, ActivityIndicator, I18nManager } from 'react-native';
import Icon from 'react-native-vector-icons/Feather';
import { ShopifyNavigation } from '@appmaker-xyz/shopify';
import { isEmpty } from 'lodash';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { createStyleSheet, useStyles } from '@appmaker-xyz/react-native';
import { analytics, emitEvent } from '@appmaker-xyz/core';
import { useTranslation } from 'react-i18next';

const SearchBar = ({ setValue, value, isLoading, onAction }) => {
  const textInputRef = useRef(null);
  const onChangeText = (text) => {
    setValue(text);
  };
  const insets = useSafeAreaInsets();
  const { styles } = useStyles(stylesheet);
  const { t } = useTranslation();

  useEffect(() => {
    const focusTimeout = setTimeout(() => {
      if (textInputRef.current) {
        textInputRef.current.focus();
      }
    }, 100);
    
    return () => clearTimeout(focusTimeout);
  }, []);

  return (
    <View style={{ ...styles.container, marginTop: insets?.top }}>
      <Icon
        name={I18nManager.isRTL ? 'arrow-right' : 'arrow-left'}
        size={20}
        style={styles.icon}
        onPress={() => onAction({ type: 'GO_BACK' })}
      />
      <TextInput
        ref={textInputRef}
        placeholder={t('Search for products, brands etc.')}
        style={styles.input}
        onChangeText={onChangeText}
        value={value}
        returnKeyType={'search'}
        // autoFocus={true}
        onSubmitEditing={() => {
          if (isEmpty(value?.trim?.())) {
            onAction({
              action: 'SHOW_MESSAGE',
              params: {
                title: 'Please enter a search query to proceed',
              },
            });
            return;
          }
          emitEvent('product.search', value);
          ShopifyNavigation.openProductSearchResult({
            query: value,
            pageTitle: value,
          });
        }}
      />
      {isLoading ? (
        <ActivityIndicator size={20} style={styles.icon} color={'#000000'} />
      ) : null}
      {value !== '' ? (
        <Icon
          name="x"
          size={20}
          style={styles.icon}
          onPress={() => onChangeText('')}
        />
      ) : null}
    </View>
  );
};

export default SearchBar;

const stylesheet = createStyleSheet((theme) => ({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.separator,
  },
  input: {
    height: 46,
    flexGrow: 1,
    paddingHorizontal: 10,
    fontFamily: theme.fontFamily.regular,
    textAlign: I18nManager.isRTL ? 'right' : 'left',
  },
  icon: {
    padding: 8,
  },
}));
