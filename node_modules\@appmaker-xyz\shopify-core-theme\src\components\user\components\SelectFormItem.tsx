import React from 'react';
import { Control, Controller } from 'react-hook-form';
import { TextInputProps } from 'react-native';
import { Select } from '@appmaker-xyz/ui';
import { useTranslation } from 'react-i18next';

type Props = {
  control: Control;
  name: string;
  label: string;
  style?: any;
} & TextInputProps;
export function SelectFormItem({
  control,
  name,
  label,
  style = {},
  ...props
}: Props) {
  const { t } = useTranslation();
  return (
    <Controller
      control={control}
      name={name}
      render={({ field: { onChange, value, onBlur, ref }, fieldState }) => (
        <Select
          style={style ? style : {}}
          error={fieldState.error && t(fieldState.error.message)}
          inputRef={ref}
          onChange={onChange}
          value={value}
          {...props}
        />
      )}
    />
  );
}
