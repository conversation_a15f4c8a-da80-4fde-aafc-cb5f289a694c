import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import {
  appStorageApi,
  runDataSource,
  getExtensionConfig,
} from '@appmaker-xyz/core';
import {
  Layout,
  AppmakerText,
  AppImage,
  Button,
  AppTouchable,
} from '@appmaker-xyz/uikit';
import { usePageState } from '@appmaker-xyz/core';

function Radio(props) {
  return (
    <AppTouchable
      style={props.option}
      onPress={() => props.setActive(!props.active)}>
      <AppmakerText category="bodySubText">{props.title}</AppmakerText>
      <AppmakerText category="bodyParagraphBold">{props.price}</AppmakerText>
    </AppTouchable>
  );
}

async function getProductData(productIds) {
  const ids = productIds.map((product) => {
    return `gid://shopify/Product/${product.product_id}`;
  });
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    {
      dataSource,
    },
    {
      methodName: 'products',
      params: {
        ids,
      },
    },
  );
  return response.data.data.products;
}

const ChumakInsiderFooter = ({ onPress, pageDispatch, onAction }) => {
  const [productResp, setProductResp] = useState([]);
  const pIds = getExtensionConfig('xeno', 'productids', []);
  useEffect(() => {
    async function fetchData() {
      const productResp = await getProductData(pIds);
      setProductResp(productResp);
    }
    fetchData();
  }, [pIds]);

  const [active, setActive] = useState(-1);
  const [loading, setLoading] = useState(false);
  const setpageState = usePageState((state) => state.setState);
  const { user, checkout } = appStorageApi().getState();

  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, active });

  const onAddtoCart = async ({ quantity }) => {
    setLoading(true);
    try {
      let hasXenoProduct = checkout?.lineItems?.edges.some(
        (x) => x?.node?.variant?.product?.productType === 'membership',
      );
      if (hasXenoProduct) {
        // UPDATE_CART_V2
        const node = checkout?.lineItems?.edges.find(
          (x) => x?.node?.variant?.product?.productType === 'membership',
        )?.node;
        let productData = undefined;
        if (productResp?.length > 0) {
          productData = productResp.edges.find(
            (i) => i?.node?.id === node.variant?.product?.id,
          );
        }
        const remove = await onAction({
          action: 'UPDATE_CART_V2',
          params: {
            product: productData || node?.variant?.product,
            variant: productData?.node?.variants?.edges[0] || node?.variant,
            quantity: 0,
            lineItemId: node?.id,
            // product: data,
          },
        });
        console.log(remove);
      }
      let params = {
        action: 'ADD_TO_CART_V2',
        params: {
          variant: productResp?.edges[active]?.node?.variants?.edges[0],
          product: productResp?.edges[active]?.node,
          quantity: 1,
          customAttributes: { key: '_membership', value: '_membership' },
        },
      };
      const cartResp = await onAction(params);
      if (cartResp?.status === 'success') {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message || 'Cart Updated',
          },
        });
      } else {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, Please try again',
          },
        });
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
      console.log(e);
    }
  };
  // split a string by - and get the last element
  const getLastElement = (str) => {
    return str.split('-').pop();
  };
  return (
    <Layout style={styles.container}>
      <Layout style={styles.ctaContainer}>
        <Layout style={styles.becomeInsiderBadge}>
          <AppmakerText category="bodyParagraphBold">BECOME AN</AppmakerText>
          <AppImage
            uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
            style={styles.insiderImage}
          />
          <AppmakerText category="bodyParagraphBold">MEMBER NOW!</AppmakerText>
        </Layout>
        <Layout style={styles.options}>
          {productResp.edges && productResp.edges.length > 0
            ? productResp.edges.map((item, index) => {
                return (
                  <Radio
                    active={active === index}
                    setActive={() => setActive(index)}
                    option={{
                      ...styles.option,
                      borderColor: active === index ? color.dark : color.light,
                    }}
                    title={getLastElement(item?.node?.title)}
                    price={`${item?.node?.priceRange.maxVariantPrice.currencyCode} ${item?.node?.priceRange.maxVariantPrice.amount}`}
                  />
                );
              })
            : null}
        </Layout>
        <Button
          onPress={() => onAddtoCart({ quantity: 1 })}
          baseSize={true}
          block={true}
          status={'dark'}
          loading={loading}
          wholeContainerStyle={styles.button}>
          ADD TO CART
        </Button>
      </Layout>
    </Layout>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#B6DCC9',
    },
    infoContainer: {
      paddingHorizontal: spacing.base,
      paddingBottom: spacing.md,
    },
    image: {
      width: 120,
      height: 80,
      resizeMode: 'contain',
      marginBottom: spacing.xl,
      alignSelf: 'center',
    },
    insiderBadge: {
      flexDirection: 'row',
      backgroundColor: color.white,
      alignSelf: 'flex-start',
      alignItems: 'center',
      padding: spacing.mini,
      marginBottom: spacing.base,
    },
    insiderImage: {
      width: 90,
      height: 30,
      resizeMode: 'contain',
      marginRight: spacing.mini,
    },
    content: {
      marginBottom: spacing.base,
    },
    ctaContainer: {
      backgroundColor: color.white,
      flex: 1,
      padding: spacing.base,
      alignItems: 'center',
      justifyContent: 'space-evenly',
    },
    becomeInsiderBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: '#B6DCC9',
      padding: spacing.mini,
      width: '100%',
      justifyContent: 'center',
    },
    options: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      width: '100%',
    },
    option: {
      borderWidth: 1,
      paddingVertical: spacing.small,
      paddingHorizontal: spacing.base,
      alignItems: 'center',
      shadowColor: '#000',
    },
    button: {
      borderRadius: 0,
    },
  });

export default ChumakInsiderFooter;
