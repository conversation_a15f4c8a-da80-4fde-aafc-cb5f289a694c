import React from 'react';
import OrderList from './OrderList';
// import { OrderList } from '@appmaker-xyz/shopify';

type OrderListProp = {
    BlockItemRender: React.FC<any>;
    onAction: () => any;
    loadingComponent?: () => React.ReactElement;
    numColumns?: number;
    beforeOrderRender?: (items: any[]) => any[];
    ordersLimit?: number;
  };

// This component wraps OrderList and can implement beforeOrderRender later if needed
const OrderListWrapper: React.FC<OrderListProp> = (props) => {
  return (
    <OrderList
      {...props}
    />
  );
};

export default OrderListWrapper;
