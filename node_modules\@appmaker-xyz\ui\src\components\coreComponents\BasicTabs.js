import React from 'react';
import { useState } from 'react';
import { View, Pressable, FlatList, StyleSheet, Image } from 'react-native';
import { ThemeText } from '../atoms';
import { get } from 'lodash';
import { SvgUri } from 'react-native-svg';

const BasicTabs = (props) => {
  const { attributes, innerBlocks, BlocksView, onAction } = props;
  const [activeIndex, setActiveIndex] = useState(0);
  const {
    gap = 6,
    title,
    customData,
    customDataTabTitle,
    hideTitle,
    blockContainerStyle,
    blockTitleTextStyle,
    tabContainerStyle,
    dataContainerStyle,
    activeTabStyle,
    defaultTabStyle,
    activeTabTextStyle,
    defaultTabTextStyle,
    itemSeparatorStyle,
    titleIconStyle,
    titleIconActiveStyle,
    titleIconSize = 16,
  } = attributes;

  const isCustomData = customData && customData.length > 0;
  const finalBlocks = isCustomData
    ? innerBlocks
    : innerBlocks[activeIndex]?.innerBlocks;

  const ProductScroller = () => {
    return (
      <BlocksView
        onAction={onAction}
        customBlockData={isCustomData ? customData[activeIndex] : null}
        inAppPage={{
          blocks: finalBlocks,
          attributes: { renderType: 'normal', padding: 0 },
        }}
      />
    );
  };

  // Component for rendering icons
  const TabIcon = ({ source, style, iconSize = titleIconSize, isActive }) => {
    if (!source) return null;

    if (typeof source === 'string' && source?.toLowerCase()?.includes('.svg')) {
      return (
        <SvgUri
          width={iconSize}
          height={iconSize}
          uri={source}
          style={[style, isActive ? titleIconActiveStyle : titleIconStyle]}
        />
      );
    }

    return (
      <Image
        source={typeof source === 'string' ? { uri: source } : source}
        style={[
          {
            width: iconSize,
            height: iconSize,
            resizeMode: 'contain',
          },
          style,
          isActive ? titleIconActiveStyle : titleIconStyle,
        ]}
      />
    );
  };

  return (
    <View style={blockContainerStyle}>
      {!hideTitle && title ? (
        <ThemeText size="md" fontFamily="medium" style={blockTitleTextStyle}>
          {title}
        </ThemeText>
      ) : null}
      <FlatList
        style={[styles.tab]}
        horizontal
        showsHorizontalScrollIndicator={false}
        data={isCustomData ? customData : innerBlocks}
        contentContainerStyle={[styles.tabContainer, tabContainerStyle]}
        ItemSeparatorComponent={() => (
          <View style={{ width: gap, ...itemSeparatorStyle }} />
        )}
        renderItem={({ item, index }) => {
          const customTitle = customData
            ? get(item, customDataTabTitle, '')
            : item?.attributes?.title;
          const leftIconSource = item?.attributes?.leftIcon?.url;
          const rightIconSource = item?.attributes?.rightIcon?.url;
          return (
            <Pressable
              style={[
                styles.tabButton,
                index === activeIndex
                  ? [styles.tabActive, activeTabStyle]
                  : [styles.tabInActive, defaultTabStyle],
              ]}
              onPress={() => {
                setActiveIndex(index);
              }}>
              <TabIcon
                source={leftIconSource}
                style={styles.leftIcon}
                isActive={index === activeIndex}
              />
              <ThemeText
                style={[
                  styles.tabText,
                  index === activeIndex
                    ? [styles.tabActiveTxt, activeTabTextStyle]
                    : [styles.tabInActiveTxt, defaultTabTextStyle],
                ]}>
                {customTitle || `Tab ${index + 1}`}
              </ThemeText>
              <TabIcon
                source={rightIconSource}
                style={styles.rightIcon}
                isActive={index === activeIndex}
              />
            </Pressable>
          );
        }}
      />
      <View style={dataContainerStyle}>
        <ProductScroller />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  tabActive: {
    paddingHorizontal: 22,
    paddingVertical: 10,
    backgroundColor: '#F5F5F5',
  },
  tabInActive: {
    paddingHorizontal: 22,
    paddingVertical: 10,
  },
  tabActiveTxt: {
    color: 'black',
    fontSize: 16,
  },
  tabInActiveTxt: {
    color: 'black',
    fontSize: 16,
  },
  headingText: {
    paddingBottom: 8,
    paddingTop: 12,
    textAlign: 'center',
    fontSize: 22,
  },
  tab: {
    alignSelf: 'center',
    marginBottom: 12,
  },
  tabContainer: {
    paddingHorizontal: 12,
  },
  tabButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tabText: {
    marginHorizontal: 8,
  },
  leftIcon: {
    marginRight: 4,
  },
  rightIcon: {
    marginLeft: 4,
  },
});

export default BasicTabs;
