import React, { useState, useEffect } from 'react';
import { StyleSheet } from 'react-native';
import { useApThemeState } from '@appmaker-xyz/uikit/src/theme/ThemeContext';
import {
  appStorageApi,
  runDataSource,
  getExtensionConfig,
} from '@appmaker-xyz/core';
import {
  Layout,
  AppmakerText,
  AppImage,
  Button,
  AppTouchable,
} from '@appmaker-xyz/uikit';
import { usePageState } from '@appmaker-xyz/core';

function Radio(props) {
  return (
    <AppTouchable
      style={props.option}
      onPress={() => props.setActive(!props.active)}>
      <AppmakerText category="bodySubText">{props.title}</AppmakerText>
      <Layout style={props.styles.row}>
        <AppmakerText category="bodyParagraphBold">{props.price}</AppmakerText>
        <Layout style={props.styles.radioButtonStyle}>
          <Layout style={props.radioStyle} />
        </Layout>
      </Layout>
    </AppTouchable>
  );
}

async function getProductData(productIds) {
  const ids = productIds.map((product) => {
    return `gid://shopify/Product/${product.product_id}`;
  });
  const dataSource = {
    attributes: {},
    source: 'shopify',
  };
  const [response] = await runDataSource(
    {
      dataSource,
    },
    {
      methodName: 'products',
      params: {
        ids,
      },
    },
  );
  return response.data.data.products;
}

const ChumbakInsiderCart = ({ onPress, pageDispatch, onAction }) => {
  const [productResp, setProductResp] = useState([]);
  const pIds = getExtensionConfig('xeno', 'productids', []);
  useEffect(() => {
    async function fetchData() {
      const productResp = await getProductData(pIds);
      setProductResp(productResp);
    }
    fetchData();
  }, [pIds]);

  const [active, setActive] = useState(-1);
  const [loading, setLoading] = useState(false);

  const { user, checkout } = appStorageApi().getState();
  const { color, spacing } = useApThemeState();
  const styles = allStyles({ color, spacing, active });
  const getLastElement = (str) => {
    return str.split('-').pop();
  };
  const onAddtoCart = async ({ quantity }) => {
    setLoading(true);
    try {
      let hasXenoProduct = checkout?.lineItems?.edges.some(
        (x) => x?.node?.variant?.product?.productType === 'membership',
      );
      if (hasXenoProduct) {
        const remove = await onAction({
          action: 'UPDATE_CART_QUANTITY',
          params: {
            product: {
              id: checkout?.lineItems?.edges.find(
                (x) => x?.node?.variant?.product?.productType === 'membership',
              )?.node?.id,
            },
            quantity: 0,
            // product: data,
          },
        });
        console.log(remove);
      }
      // if(count > quantity) {}
      let params = {
        action: 'ADD_TO_CART_V2',
        params: {
          variant: productResp.edges[active]?.node?.variants?.edges[0],
          product: productResp.edges[active],
          quantity: 1,
          fromList: true,
          customAttributes: { key: '_membership', value: '_membership' },
          updateCartPageStateRequired: true,
          // product: data,
        },
      };
      const cartResp = await onAction(params);
      if (cartResp?.status === 'success') {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: cartResp?.message,
          },
        });
        onAction({
          action: 'GO_BACK',
        });
      } else {
        onAction({
          action: 'SHOW_MESSAGE',
          params: {
            title: 'Something went wrong, Please try again',
          },
        });
      }
      setLoading(false);
    } catch (e) {
      setLoading(false);
      console.log(e);
    }
  };
  return (
    <Layout style={styles.container}>
      <AppImage
        uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/popup_logo-removebg-preview_1024x1024.png"
        style={styles.image}
      />
      <Layout style={styles.becomeInsiderBadge}>
        <AppmakerText category="bodyParagraphBold">JOIN</AppmakerText>
        <AppImage
          uri="https://cdn.shopify.com/s/files/1/0601/1093/0098/files/chumbak_pdp_logo-removebg-preview_1024x1024.png"
          style={styles.insiderImage}
        />
        <AppmakerText category="bodyParagraphBold">TO ENJOY</AppmakerText>
      </Layout>
      <AppmakerText status="demiDark">
        Free Priority Shipping | Additional Discounts Exclusive Access | Gifts &
        More
      </AppmakerText>
      <Layout style={styles.options}>
        {productResp.edges && productResp.edges.length > 0
          ? productResp.edges.map((item, index) => {
              return (
                <Radio
                  active={active === index}
                  setActive={() => setActive(index)}
                  styles={styles}
                  option={{
                    ...styles.option,
                    borderColor: active === index ? color.grey : color.light,
                  }}
                  radioStyle={{
                    ...styles.radioStyle,
                    backgroundColor:
                      active === index ? color.dark : 'transparent',
                  }}
                  title={getLastElement(item?.node?.title)}
                  price={`${item?.node?.priceRange.maxVariantPrice.currencyCode} ${item?.node?.priceRange.maxVariantPrice.amount}`}
                />
              );
            })
          : null}
      </Layout>
      <AppmakerText status="demiDark">
        Your membership now gets automatically renewed on the last day of active
        plan.
      </AppmakerText>
      <Button
        onPress={() => onAddtoCart({ quantity: 1 })}
        baseSize={true}
        block={true}
        loading={loading}
        status={active !== -1 ? 'dark' : 'demiDark'}
        wholeContainerStyle={styles.button}>
        ADD TO CART
      </Button>
    </Layout>
  );
};

const allStyles = ({ spacing, color, active }) =>
  StyleSheet.create({
    container: {
      backgroundColor: '#B6DCC9',
      padding: spacing.base,
      alignItems: 'center',
      justifyContent: 'space-evenly',
    },
    image: {
      width: 120,
      height: 80,
      resizeMode: 'contain',
      marginBottom: spacing.xl,
      alignSelf: 'center',
    },
    insiderImage: {
      width: 120,
      height: 30,
      resizeMode: 'contain',
      marginRight: spacing.mini,
    },
    becomeInsiderBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      backgroundColor: color.white,
      padding: spacing.mini,
      width: '100%',
      justifyContent: 'center',
    },
    options: {
      alignItems: 'center',
      width: '100%',
    },
    option: {
      borderColor: active ? color.grey : color.light,
      backgroundColor: color.white,
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: spacing.base,
      borderWidth: 1,
      paddingVertical: spacing.md,
      paddingHorizontal: spacing.base,
      alignItems: 'center',
      shadowColor: '#000',
      width: '100%',
    },
    radioButtonStyle: {
      borderColor: 'black',
      borderWidth: 1,
      borderRadius: 16,
      marginLeft: spacing.small,
      padding: 2,
    },
    radioStyle: {
      width: 16,
      height: 16,
      borderRadius: 16,
      backgroundColor: active ? color.dark : 'transparent',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    button: {
      borderRadius: 0,
    },
  });

export default ChumbakInsiderCart;
