import React, { useState } from 'react';
import { MobileInputCard, OtpInputCard } from '@appmaker-xyz/ui';
// import { View } from 'react-native';
import { useOtpLogin, useUser } from '@appmaker-xyz/shopify';

const MobileInput = (props) => {
  const [formattedValue, setFormattedValue] = useState('+91');
  const [textMobile, setMobileNumber] = useState('');

  const {
    currentStep, // 'send' or 'verify'
    error,
    sendCode,
    verifyCode,
    sendStatus, // 'loading', 'success', 'error'
    sendErrorMessage,
    verifyStatus, // 'loading', 'success', 'error'
    verifyErrorMessage,
    resendStatus, // 'loading', 'success', 'error'
    resendErrorMessage,
    // resendOtp,
    canResend,
    resendCode,
    reset,
    failedAttemptCount,
    // verifyErrorMessage,
  } = useOtpLogin({
    handleAction: props?.onAction,
  });
  const onPressRevieveOtp = () => {
    textMobile && sendCode(formattedValue, textMobile);
    if (!textMobile) {
      props?.onAction &&
        props?.onAction({
          action: 'SHOW_MESSAGE',
          params: { title: 'Please enter your mobile number to continue' },
        });
    }
  };
  return currentStep === 'send' || sendStatus === 'loading' ? (
    <MobileInputCard
      defaultValue={textMobile}
      onPress={onPressRevieveOtp}
      isLoading={sendStatus === 'loading'}
      buttonColor="#EF6C00"
      onChangeText={(text) => {
        setMobileNumber(text);
      }}
      onChangeFormattedText={(text) => {
        setFormattedValue(text);
      }}
    />
  ) : (
    <OtpInputCard
      verifyCode={verifyCode}
      phoneNumber={formattedValue}
      onEditNumber={reset}
      verifyErrorMessage={verifyErrorMessage}
      failedAttemptCount={failedAttemptCount}
      resendErrorMessage={resendErrorMessage}
      resendCode={resendCode}
      resendLoading={resendStatus}
      //   phoneNumber={formattedValue}
    />
  );
};

export default MobileInput;
